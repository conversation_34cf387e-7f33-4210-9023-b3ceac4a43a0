name: safari_yatri
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.17.19+25

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  go_router: ^15.1.1
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.4.0
  google_fonts: ^6.2.1
  equatable: ^2.0.7
  dartz: ^0.10.1
  get_it: ^8.0.3
  shared_preferences: ^2.5.3
  shadcn_ui: ^0.27.0
  google_maps_flutter: ^2.12.1
  flutter_svg: ^2.1.0
  lottie: ^3.3.1
  flutter_bloc: ^9.1.1
  geolocator: ^14.0.0
  logger: ^2.5.0
  fluttertoast: ^8.2.4
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  introduction_screen: ^3.1.17
  geocoding: ^4.0.0
  permission_handler: ^12.0.0+1
  gap: ^3.0.1
  flutter_polyline_points: ^2.1.0
  collection: ^1.19.1
  flutter_secure_storage: ^9.2.4
  hive: ^2.2.3
  path_provider: ^2.1.5
  pinput: ^5.0.1
  image_picker: ^1.1.2
  flutter_dotenv: ^5.2.1
  flutter_animate: ^4.5.2
  intl: ^0.20.2
  flex_color_scheme: ^8.2.0
  package_info_plus: ^8.3.0
  shimmer: ^3.0.0
  flutter_image_compress: ^2.4.0
  audioplayers: ^6.5.0
  vibration: ^3.1.3
  showcaseview: ^4.0.1
  url_launcher: ^6.3.1
  firebase_core: ^3.14.0
  firebase_analytics: ^11.5.1
  firebase_crashlytics: ^4.3.9


dev_dependencies:
  flutter_launcher_icons: ^0.14.3
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.15
  freezed: ^3.0.6
  json_serializable: ^6.9.5

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/logo/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/logo/logo.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  generate: true
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/logo/
    - assets/svg/
    - assets/lottie/
    - assets/audio/
    - assets/markers/
    - .env
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
