import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/locale_cubit/locale_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/l10n/app_localizations.dart';
import '../../core/constant/image_constant.dart';
import '../../core/router/app_route_names.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_styles.dart';
import '../../core/theme/app_text_theme.dart';

class LanguageSelectionPage extends StatefulWidget {
  const LanguageSelectionPage({super.key});

  @override
  State<LanguageSelectionPage> createState() => _LanguageSelectionPageState();
}

class _LanguageSelectionPageState extends State<LanguageSelectionPage> {
  late String selectedLanguageCode;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    selectedLanguageCode = Localizations.localeOf(context).languageCode;
  }

  void _onLanguageChanged(String languageCode) {
    setState(() {
      selectedLanguageCode = languageCode;
    });

    sl<LocaleCubit>().changeLocale(Locale(languageCode));
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(top: AppStyles.space32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Image.asset(ImageConstant.appLogo, height: 200, width: 300),
              Padding(
                padding: const EdgeInsets.all(AppStyles.space20),
                child: Column(
                  children: [
                    Text(
                      l10n.selectedLanguage,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppStyles.space20),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.borderColorLight),
                        borderRadius: AppStyles.radiusSm,
                      ),
                      child: Column(
                        children: [
                          _buildLanguageTile('English', 'en'),
                          AppStyles.dividerThin,
                          _buildLanguageTile('नेपाली', 'ne'),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppStyles.space8),
                    Text(l10n.chooseLater),
                    const SizedBox(height: AppStyles.space20),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          context.goNamed(AppRoutesName.onBoarding);
                        },
                        child: Text(l10n.next),
                      ),
                    ),
                  ],
                ),
              ),
              Image.asset(ImageConstant.riskshawJPG),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageTile(String title, String languageCode) {
    return ListTile(
      title: Text(title, style: AppTextThemes.lightTextTheme.titleSmall),
      trailing: Radio<String>(
        value: languageCode,
        groupValue: selectedLanguageCode,
        onChanged: (value) {
          if (value != null) _onLanguageChanged(value);
        },
      ),
    );
  }
}
