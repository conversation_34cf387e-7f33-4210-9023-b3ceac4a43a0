import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/once_cubit/once_cubit.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/l10n/app_localizations.dart'; // import native localization

class OnBoardingPage extends StatefulWidget {
  const OnBoardingPage({super.key});

  @override
  State<OnBoardingPage> createState() => _OnBoardingPageState();
}

class _OnBoardingPageState extends State<OnBoardingPage> {
  final introKey = GlobalKey<IntroductionScreenState>();

  void _onIntroEnd() {
    sl<OnceCubit>().completedOnBoaring();
    context.goNamed(AppRoutesName.signIn);
  }

  void _onIntroSkip() {
    introKey.currentState?.animateScroll(
      introKey.currentState!.getPagesLength() - 1,
    );
  }

  Widget _buildImage(String assetName) {
    return SvgPicture.asset(assetName, width: 270);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    final bodyStyle = Theme.of(context).textTheme.bodyLarge;
    final titleStyle = Theme.of(context).textTheme.headlineMedium;

    final pageDecoration = PageDecoration(
      imagePadding: const EdgeInsets.all(0),
      bodyAlignment: Alignment.topCenter,
      titlePadding: const EdgeInsets.all(AppStyles.space12),
      pageMargin: const EdgeInsets.all(0),
      titleTextStyle: titleStyle ?? const TextStyle(),
      bodyTextStyle: bodyStyle ?? const TextStyle(),
      pageColor: Colors.white,
    );

    return IntroductionScreen(
      key: introKey,
      pages: [
        PageViewModel(
          title: l10n.onBoardingfirstScreenTitle,
          body: l10n.onBoardingfirstScreenDesc,
          image: _buildImage(ImageConstant.firstOnBoard),
          decoration: pageDecoration,
        ),
        PageViewModel(
          title: l10n.onBoardingSecondScreenTitle,
          body: l10n.onBoardingSecondScreenDesc,
          image: _buildImage(ImageConstant.secondOnBoard),
          decoration: pageDecoration,
        ),
        PageViewModel(
          title: l10n.onBoardingThirdScreenTitle,
          body: l10n.onBoardingThirdScreenDesc,
          image: _buildImage(ImageConstant.thirdOnBoard),
          decoration: pageDecoration,
        ),
      ],
      onDone: _onIntroEnd,
      onSkip: _onIntroSkip,
      showSkipButton: true,
      skip: Text(l10n.skip),
      next: const Icon(Icons.arrow_forward),
      done: Text(
        l10n.done,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      dotsDecorator: const DotsDecorator(
        size: Size.square(10.0),
        activeSize: Size(22.0, 10.0),
        activeShape: RoundedRectangleBorder(borderRadius: AppStyles.radiusLg),
      ),
    );
  }
}
