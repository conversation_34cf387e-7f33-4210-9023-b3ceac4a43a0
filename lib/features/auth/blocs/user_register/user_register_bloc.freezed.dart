// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_register_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserRegisterEvent {
  UserRegisterForm get userForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserRegisterEvent &&
            (identical(other.userForm, userForm) ||
                other.userForm == userForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userForm);

  @override
  String toString() {
    return 'UserRegisterEvent(userForm: $userForm)';
  }
}

/// @nodoc

class _SignUP implements UserRegisterEvent {
  const _SignUP(this.userForm);

  @override
  final UserRegisterForm userForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SignUP &&
            (identical(other.userForm, userForm) ||
                other.userForm == userForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userForm);

  @override
  String toString() {
    return 'UserRegisterEvent.signUp(userForm: $userForm)';
  }
}
