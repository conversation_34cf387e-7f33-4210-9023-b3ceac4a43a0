// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'change_password_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChangePasswordEvent {
  PasswordChangeForm get passwordForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangePasswordEvent &&
            (identical(other.passwordForm, passwordForm) ||
                other.passwordForm == passwordForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordForm);

  @override
  String toString() {
    return 'ChangePasswordEvent(passwordForm: $passwordForm)';
  }
}

/// @nodoc

class _PasswordChange implements ChangePasswordEvent {
  const _PasswordChange(this.passwordForm);

  @override
  final PasswordChangeForm passwordForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PasswordChange &&
            (identical(other.passwordForm, passwordForm) ||
                other.passwordForm == passwordForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordForm);

  @override
  String toString() {
    return 'ChangePasswordEvent.passwordChange(passwordForm: $passwordForm)';
  }
}
