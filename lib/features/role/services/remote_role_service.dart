import 'package:safari_yatri/core/constant/user_roles.dart';
import 'package:shared_preferences/shared_preferences.dart';

///yesle chaii locally user ko remote/server role k ho
///store garney kaam garxa
class RemoteRoleService {
  static RemoteRoleService? _instance;
  static const String roleKey = 'remote_user_role';
  static const String riderRequestPendingKey = 'ride_request_pending';
  static const String isBecomeRiderKey = 'is_become_rider';

  final SharedPreferences _prefs;

  RemoteRoleService._(this._prefs);

  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _instance ??= RemoteRoleService._(prefs);
  }

  static RemoteRoleService get instance {
    if (_instance == null) {
      throw Exception(
        "RemoteRoleService not initialized. Call RemoteRoleService.init() first.",
      );
    }
    return _instance!;
  }

  Future<void> setUserRole(String role) async {
    await _prefs.setString(roleKey, role);
  }

  String? getUserRole() {
    return _prefs.getString(roleKey);
  }

  ///generally helps to set role weather  application is pending or not
  Future<void> setRiderRequestPending(String role) async {
    if (role == kPendingRequestForRiderRole) {
      await _prefs.setBool(riderRequestPendingKey, true);
    }
    if (role == kRiderRole) {
      await _prefs.setBool(isBecomeRiderKey, true);
    }
  }

  Future<bool> isBecomeRider() async {
    return _prefs.getBool(isBecomeRiderKey) ?? false;
  }

  bool isPassengerRequestForRiderPending() {
    return _prefs.getBool(riderRequestPendingKey) ?? false;
  }

  Future<void> clearRiderRequestPending() async {
    await _prefs.remove(riderRequestPendingKey);
    await _prefs.remove(isBecomeRiderKey);
  }

  Future<void> clearUserRole() async {
    await _prefs.remove(roleKey);
    await _prefs.remove(isBecomeRiderKey);
  }
}
