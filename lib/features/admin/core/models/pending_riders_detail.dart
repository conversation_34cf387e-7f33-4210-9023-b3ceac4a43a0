class RidersDetailsModel {
  final String userName;
  final String userAddress;
  final String gender;
  final String emailAddress;
  final String phoneNo;
  final int userId;
  final double homeLatitude;
  final double homeLongitude;
  final double currentLatitude;
  final double currentLongitude;
  final String loginId;
  final String loginStatus;
  final String userType;
  final String registeredDate;
  final String registeredTime;
  final String otpSentDate;
  final String otpSentTime;
  final String activationDate;
  final String activationTime;
  final double walletBalance;
  final String? profilePicture;
  final String riderCitizenCardFront;
  final String riderCitizenCardBack;
  final String riderIdentityPhoto;
  final int vehicleTypeId;
  final String vehicleNo;
  final String vehiclePhoto;
  final String ownerName;
  final String ownerPhone;
  final String blueBookPhoto;
  final String riderAppliedDate;
  final String riderAppliedTime;
  final String? riderVerifiedDate;
  final String? riderVerifiedTime;
  final bool isRiderAvailable;
  final int? riderVerifiedBy;

  RidersDetailsModel({
    required this.userName,
    required this.userAddress,
    required this.gender,
    required this.emailAddress,
    required this.phoneNo,
    required this.userId,
    required this.homeLatitude,
    required this.homeLongitude,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.loginId,
    required this.loginStatus,
    required this.userType,
    required this.registeredDate,
    required this.registeredTime,
    required this.otpSentDate,
    required this.otpSentTime,
    required this.activationDate,
    required this.activationTime,
    required this.walletBalance,
    this.profilePicture,
    required this.riderCitizenCardFront,
    required this.riderCitizenCardBack,
    required this.riderIdentityPhoto,
    required this.vehicleTypeId,
    required this.vehicleNo,
    required this.vehiclePhoto,
    required this.ownerName,
    required this.ownerPhone,
    required this.blueBookPhoto,
    required this.riderAppliedDate,
    required this.riderAppliedTime,
    this.riderVerifiedDate,
    this.riderVerifiedTime,
    required this.isRiderAvailable,
    required this.riderVerifiedBy,
  });

  factory RidersDetailsModel.fromJson(Map<String, dynamic> json) {
    return RidersDetailsModel(
      userName: json['UserName'],
      userAddress: json['UserAddress'],
      gender: json['Gender'],
      emailAddress: json['EmailAddress'],
      phoneNo: json['PhoneNo'],
      userId: json['UserId'],
      homeLatitude: (json['HomeLatitude'] as num).toDouble(),
      homeLongitude: (json['HomeLongitude'] as num).toDouble(),
      currentLatitude: (json['CurrentLatitude'] as num).toDouble(),
      currentLongitude: (json['CurrentLongitude'] as num).toDouble(),
      loginId: json['LoginId'],
      loginStatus: json['LoginStatus'],
      userType: json['UserType'],
      registeredDate: json['RegisteredDate'],
      registeredTime: json['RegisteredTime'],
      otpSentDate: json['OtpSentDate'],
      otpSentTime: json['OtpSentTime'],
      activationDate: json['ActivationDate'],
      activationTime: json['ActivationTime'],
      walletBalance: (json['WalletBalance'] as num).toDouble(),
      profilePicture: json['ProfilePicture'],
      riderCitizenCardFront: json['RiderCitizenCardFront'],
      riderCitizenCardBack: json['RiderCitizenCardBack'],
      riderIdentityPhoto: json['RiderIdentityPhoto'],
      vehicleTypeId: json['VehicleTypeId'],
      vehicleNo: json['VehicleNo'],
      vehiclePhoto: json['VehiclePhoto'],
      ownerName: json['OwnerName'],
      ownerPhone: json['OwnerPhone'],
      blueBookPhoto: json['BlueBookPhoto'],
      riderAppliedDate: json['RiderAppliedDate'],
      riderAppliedTime: json['RiderAppliedTime'],
      riderVerifiedDate: json['RiderVerifiedDate'],
      riderVerifiedTime: json['RiderVerifiedTime'],
      isRiderAvailable: json['IsRiderAvailable'],
      riderVerifiedBy: json['RiderVerifiedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'UserName': userName,
      'UserAddress': userAddress,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'PhoneNo': phoneNo,
      'UserId': userId,
      'HomeLatitude': homeLatitude,
      'HomeLongitude': homeLongitude,
      'CurrentLatitude': currentLatitude,
      'CurrentLongitude': currentLongitude,
      'LoginId': loginId,
      'LoginStatus': loginStatus,
      'UserType': userType,
      'RegisteredDate': registeredDate,
      'RegisteredTime': registeredTime,
      'OtpSentDate': otpSentDate,
      'OtpSentTime': otpSentTime,
      'ActivationDate': activationDate,
      'ActivationTime': activationTime,
      'WalletBalance': walletBalance,
      'ProfilePicture': profilePicture,
      'RiderCitizenCardFront': riderCitizenCardFront,
      'RiderCitizenCardBack': riderCitizenCardBack,
      'RiderIdentityPhoto': riderIdentityPhoto,
      'VehicleTypeId': vehicleTypeId,
      'VehicleNo': vehicleNo,
      'VehiclePhoto': vehiclePhoto,
      'OwnerName': ownerName,
      'OwnerPhone': ownerPhone,
      'BlueBookPhoto': blueBookPhoto,
      'RiderAppliedDate': riderAppliedDate,
      'RiderAppliedTime': riderAppliedTime,
      'RiderVerifiedDate': riderVerifiedDate,
      'RiderVerifiedTime': riderVerifiedTime,
      'IsRiderAvailable': isRiderAvailable,
      'RiderVerifiedBy': riderVerifiedBy,
    };
  }
}
