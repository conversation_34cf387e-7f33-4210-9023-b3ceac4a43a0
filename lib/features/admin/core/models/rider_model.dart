class RiderApplication {
  final int userId;
  final String phoneNo;
  final String userName;
  final String userAddress;
  final String gender;
  final String emailAddress;
  final double homeLatitude;
  final double homeLongitude;
  final double currentLatitude;
  final double currentLongitude;
  final String loginId;
  final String loginStatus;
  final String userType;
  final String registeredDate;
  final String registeredTime;
  final String? otpSentDate;
  final String? otpSentTime;
  final String? activationDate;
  final String? activationTime;
  final double walletBalance;
  final String? profilePicture;

  RiderApplication({
    required this.userId,
    required this.phoneNo,
    required this.userName,
    required this.userAddress,
    required this.gender,
    required this.emailAddress,
    required this.homeLatitude,
    required this.homeLongitude,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.loginId,
    required this.loginStatus,
    required this.userType,
    required this.registeredDate,
    required this.registeredTime,
    this.otpSentDate,
    this.otpSentTime,
    this.activationDate,
    this.activationTime,
    required this.walletBalance,
     this.profilePicture,
  });

  factory RiderApplication.fromMap(Map<String, dynamic> map) {
    return RiderApplication(
      userId: map['UserId'] as int,
      phoneNo: map['PhoneNo'] as String,
      userName: map['UserName'] as String,
      userAddress: map['UserAddress'] as String,
      gender: map['Gender'] as String,
      emailAddress: map['EmailAddress'] as String,
      homeLatitude: (map['HomeLatitude'] as num).toDouble(),
      homeLongitude: (map['HomeLongitude'] as num).toDouble(),
      currentLatitude: (map['CurrentLatitude'] as num).toDouble(),
      currentLongitude: (map['CurrentLongitude'] as num).toDouble(),
      loginId: map['LoginId'] as String,
      loginStatus: map['LoginStatus'] as String,
      userType: map['UserType'] as String,
      registeredDate: map['RegisteredDate'] as String,
      registeredTime: map['RegisteredTime'] as String,

      otpSentDate: map['OtpSentDate'] as String?,
      otpSentTime: map['OtpSentTime'] as String?,
      activationDate: map['ActivationDate'] as String?,
      activationTime: map['ActivationTime'] as String?,
      walletBalance: (map['WalletBalance'] as num).toDouble(),
      profilePicture: map['ProfilePicture'],
    );
  }
}
