// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_admin_role_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetAdminRoleListEvent {
  bool get forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetAdminRoleListEvent &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode => Object.hash(runtimeType, forceFetch);

  @override
  String toString() {
    return 'GetAdminRoleListEvent(forceFetch: $forceFetch)';
  }
}

/// @nodoc

class _Get implements GetAdminRoleListEvent {
  const _Get({this.forceFetch = false});

  @override
  @JsonKey()
  final bool forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Get &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode => Object.hash(runtimeType, forceFetch);

  @override
  String toString() {
    return 'GetAdminRoleListEvent.get(forceFetch: $forceFetch)';
  }
}
