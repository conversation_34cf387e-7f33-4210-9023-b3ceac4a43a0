// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_fare_rate_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageFareRateEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageFareRateEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageFareRateEvent()';
  }
}

/// @nodoc

class _Insert implements ManageFareRateEvent {
  const _Insert(this.rate);

  final FareRateInsertRequestModel rate;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Insert &&
            (identical(other.rate, rate) || other.rate == rate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, rate);

  @override
  String toString() {
    return 'ManageFareRateEvent.insert(rate: $rate)';
  }
}

/// @nodoc

class _Update implements ManageFareRateEvent {
  const _Update(this.fareRate);

  final FareRateModel fareRate;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Update &&
            (identical(other.fareRate, fareRate) ||
                other.fareRate == fareRate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fareRate);

  @override
  String toString() {
    return 'ManageFareRateEvent.update(fareRate: $fareRate)';
  }
}
