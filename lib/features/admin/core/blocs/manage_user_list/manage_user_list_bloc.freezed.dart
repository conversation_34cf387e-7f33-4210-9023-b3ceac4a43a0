// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_user_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageUserListEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageUserListEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageUserListEvent()';
  }
}

/// @nodoc

class _InsertUser implements ManageUserListEvent {
  const _InsertUser(this.user);

  final UserModel user;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InsertUser &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @override
  String toString() {
    return 'ManageUserListEvent.insertUser(user: $user)';
  }
}

/// @nodoc

class _UpdateUser implements ManageUserListEvent {
  const _UpdateUser(this.user);

  final UserModel user;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateUser &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @override
  String toString() {
    return 'ManageUserListEvent.updateUser(user: $user)';
  }
}

/// @nodoc

class _ArchiveUser implements ManageUserListEvent {
  const _ArchiveUser(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ArchiveUser &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.archiveUser(userId: $userId)';
  }
}

/// @nodoc

class _ActivateLogin implements ManageUserListEvent {
  const _ActivateLogin(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActivateLogin &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.activateLogin(userId: $userId)';
  }
}

/// @nodoc

class _DisableLogin implements ManageUserListEvent {
  const _DisableLogin(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DisableLogin &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.disableLogin(userId: $userId)';
  }
}

/// @nodoc

class _ResetPassword implements ManageUserListEvent {
  const _ResetPassword(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ResetPassword &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.resetPassword(userId: $userId)';
  }
}

/// @nodoc

class _ChangeUserType implements ManageUserListEvent {
  const _ChangeUserType({required this.userId, required this.userType});

  final int userId;
  final String userType;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChangeUserType &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userType, userType) ||
                other.userType == userType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId, userType);

  @override
  String toString() {
    return 'ManageUserListEvent.changeUserType(userId: $userId, userType: $userType)';
  }
}

/// @nodoc

class _AcceptForRider implements ManageUserListEvent {
  const _AcceptForRider({required this.userId});

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AcceptForRider &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.acceptForRider(userId: $userId)';
  }
}

/// @nodoc

class _RejectForRider implements ManageUserListEvent {
  const _RejectForRider({required this.userId});

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RejectForRider &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.rejectForRider(userId: $userId)';
  }
}

/// @nodoc

class _UpdateRoles implements ManageUserListEvent {
  const _UpdateRoles({
    required this.userId,
    required final List<AdminRoleUpdateModel> roles,
  }) : _roles = roles;

  final int userId;
  final List<AdminRoleUpdateModel> _roles;
  List<AdminRoleUpdateModel> get roles {
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_roles);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateRoles &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(other._roles, _roles));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    const DeepCollectionEquality().hash(_roles),
  );

  @override
  String toString() {
    return 'ManageUserListEvent.updateRoles(userId: $userId, roles: $roles)';
  }
}
