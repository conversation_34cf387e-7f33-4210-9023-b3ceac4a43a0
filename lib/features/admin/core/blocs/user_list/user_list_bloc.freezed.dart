// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserListEvent {
  String get userType;
  String get loginStatus;
  bool get forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserListEvent &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, userType, loginStatus, forceFetch);

  @override
  String toString() {
    return 'UserListEvent(userType: $userType, loginStatus: $loginStatus, forceFetch: $forceFetch)';
  }
}

/// @nodoc

class _GetUserList implements UserListEvent {
  const _GetUserList({
    required this.userType,
    required this.loginStatus,
    this.forceFetch = false,
  });

  @override
  final String userType;
  @override
  final String loginStatus;
  @override
  @JsonKey()
  final bool forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GetUserList &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, userType, loginStatus, forceFetch);

  @override
  String toString() {
    return 'UserListEvent.getUserList(userType: $userType, loginStatus: $loginStatus, forceFetch: $forceFetch)';
  }
}
