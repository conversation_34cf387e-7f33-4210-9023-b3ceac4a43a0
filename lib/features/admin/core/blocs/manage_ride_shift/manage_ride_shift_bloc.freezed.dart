// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_ride_shift_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageRideShiftEvent {
  Object get rideShift;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ManageRideShiftEvent &&
            const DeepCollectionEquality().equals(other.rideShift, rideShift));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(rideShift));

  @override
  String toString() {
    return 'ManageRideShiftEvent(rideShift: $rideShift)';
  }
}

/// @nodoc

class _Update implements ManageRideShiftEvent {
  const _Update(this.rideShift);

  @override
  final RideShiftModel rideShift;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Update &&
            (identical(other.rideShift, rideShift) ||
                other.rideShift == rideShift));
  }

  @override
  int get hashCode => Object.hash(runtimeType, rideShift);

  @override
  String toString() {
    return 'ManageRideShiftEvent.update(rideShift: $rideShift)';
  }
}

/// @nodoc

class _Insert implements ManageRideShiftEvent {
  const _Insert(this.rideShift);

  @override
  final RideShiftForm rideShift;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Insert &&
            (identical(other.rideShift, rideShift) ||
                other.rideShift == rideShift));
  }

  @override
  int get hashCode => Object.hash(runtimeType, rideShift);

  @override
  String toString() {
    return 'ManageRideShiftEvent.insert(rideShift: $rideShift)';
  }
}
