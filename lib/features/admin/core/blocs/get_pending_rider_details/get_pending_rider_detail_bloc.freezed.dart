// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_pending_rider_detail_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetPendingRiderDetailEvent {
  int get riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetPendingRiderDetailEvent &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'GetPendingRiderDetailEvent(riderId: $riderId)';
  }
}

/// @nodoc

class _Get implements GetPendingRiderDetailEvent {
  const _Get(this.riderId);

  @override
  final int riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Get &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'GetPendingRiderDetailEvent.get(riderId: $riderId)';
  }
}
