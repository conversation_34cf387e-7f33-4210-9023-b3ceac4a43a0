import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/constant/user_roles.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/features/admin/core/blocs/user_detail/user_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';
import 'package:safari_yatri/features/admin/core/models/user_model.dart';
import 'package:safari_yatri/features/admin/core/widgets/admin_role_selector_widget.dart';
import 'package:safari_yatri/features/admin/core/widgets/manage_user_bloc_listener.dart';

class UserDetailPage extends StatefulWidget {
  final int userId;

  const UserDetailPage({super.key, required this.userId});

  @override
  State<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends State<UserDetailPage> {
  late final ManageUserListBloc _manageUserListBloc;
  late final UserDetailBloc _userDetailBloc;
  List<AdminRoleUpdateModel> _selectedRoles = [];

  @override
  void initState() {
    super.initState();
    _manageUserListBloc = sl<ManageUserListBloc>();
    _userDetailBloc = sl<UserDetailBloc>();

    _getCurrentUser();
  }

  void _getCurrentUser() {
    _userDetailBloc.add(UserDetailEvent.getUserById(widget.userId));
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: RefreshIndicator(
        onRefresh: () async {
          _getCurrentUser();
        },
        child: ManageUserBlocListener(
          onSuccess: () => _getCurrentUser(),
          cudUserListBloc: _manageUserListBloc,
          child: BlocBuilder<UserDetailBloc, UserDetailState>(
            bloc: _userDetailBloc,
            builder: (context, state) {
              return state.maybeWhen(
                loading: () => const Center(child: CircularProgressIndicator()),
                failure:
                    (failure) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: colorScheme.error,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error: ${failure.message}',
                            style: TextStyle(
                              color: colorScheme.error,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                loaded: (user) => _buildUserDetailView(user, colorScheme),
                orElse: () => const SizedBox.shrink(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildUserDetailView(UserModel user, ColorScheme colorScheme) {
    return CustomScrollView(
      slivers: [
        _buildSliverAppBar(user, colorScheme),
        SliverToBoxAdapter(
          child: RefreshIndicator(
            onRefresh: () async {
              _userDetailBloc.add(UserDetailEvent.getUserById(user.userId));
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildQuickActionsCard(user, colorScheme),
                  const SizedBox(height: 24),
                  _buildPersonalInfoCard(user, colorScheme),
                  const SizedBox(height: 16),
                  _buildAccountInfoCard(user, colorScheme),
                  const SizedBox(height: 24),
                  _buildDangerZoneCard(user, colorScheme),
                  const SizedBox(height: 24),

                  user.userType != kAdminRole
                      ? const SizedBox.shrink()
                      : AdminRoleSelectorWidget(
                        userId: user.userId,
                        onChanged: (roles) {
                          _selectedRoles = roles;
                        },
                      ),
                  const SizedBox(height: 100), // Bottom padding
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar(UserModel user, ColorScheme colorScheme) {
    return SliverAppBar(
      expandedHeight: 320,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,

      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.primaryContainer.withOpacity(0.8),
                colorScheme.surface,
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 60),
                _buildProfileAvatar(user, colorScheme),
                const SizedBox(height: 16),
                Text(
                  user.userName,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                _buildUserBadge(user, colorScheme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(UserModel user, ColorScheme colorScheme) {
    return Hero(
      tag: 'user_avatar_${user.userId}',
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: colorScheme.primary, width: 3),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipOval(
          child: SizedBox(
            height: 120,
            width: 120,
            child: ImageUtility.displayImageFromBase64(user.profilePicture),
          ),
        ),
      ),
    );
  }

  Widget _buildUserBadge(UserModel user, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _getUserTypeColor(user.userType),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: _getUserTypeColor(user.userType).withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getUserTypeIcon(user.userType), color: Colors.white, size: 16),
          const SizedBox(width: 6),
          Text(
            user.userType,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard(UserModel user, ColorScheme colorScheme) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionTile(
                    icon: Icons.lock_reset,
                    label: 'Reset Password',
                    onTap: () {
                      _manageUserListBloc.add(
                        ManageUserListEvent.resetPassword(user.userId),
                      );
                    },
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionTile(
                    icon:
                        user.loginStatus.toLowerCase() == 'active'
                            ? Icons.block
                            : Icons.check_circle,
                    label:
                        user.loginStatus.toLowerCase() == 'active'
                            ? 'Disable Login'
                            : 'Activate Login',
                    onTap: () {
                      if (user.loginStatus.toLowerCase() == 'active') {
                        _manageUserListBloc.add(
                          ManageUserListEvent.disableLogin(user.userId),
                        );
                      } else {
                        _manageUserListBloc.add(
                          ManageUserListEvent.activateLogin(user.userId),
                        );
                      }
                    },
                    color:
                        user.loginStatus.toLowerCase() == 'active'
                            ? colorScheme.error
                            : colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionTile({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.2)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoCard(UserModel user, ColorScheme colorScheme) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Personal Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoTile(
              icon: Icons.person_outline,
              label: 'Full Name',
              value: user.userName,
              colorScheme: colorScheme,
            ),
            _buildInfoTile(
              icon: Icons.phone,
              label: 'Phone Number',
              value: user.phoneNo,
              colorScheme: colorScheme,
            ),
            _buildInfoTile(
              icon: Icons.email_outlined,
              label: 'Email Address',
              value: user.emailAddress,
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoCard(UserModel user, ColorScheme colorScheme) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Account Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoTile(
              icon: Icons.tag,
              label: 'User ID',
              value: user.userId.toString(),
              colorScheme: colorScheme,
            ),
            _buildInfoTile(
              icon: Icons.login,
              label: 'Login ID',
              value: user.loginId,
              colorScheme: colorScheme,
            ),
            _buildInfoTile(
              icon: Icons.verified_user,
              label: 'Account Type',
              value: user.userType,
              colorScheme: colorScheme,
            ),
            _buildStatusTile(
              icon: Icons.circle,
              label: 'Login Status',
              value: user.loginStatus,
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDangerZoneCard(UserModel user, ColorScheme colorScheme) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: colorScheme.error, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Administrative Actions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.error,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDangerActionTile(
              icon:
                  user.userType == kAdminRole
                      ? Icons.person_remove
                      : Icons.admin_panel_settings,
              title:
                  user.userType == kAdminRole
                      ? 'Downgrade to Passenger'
                      : 'Promote to Admin',
              subtitle:
                  user.userType == kAdminRole
                      ? 'Remove admin privileges from this user'
                      : 'Grant admin privileges to this user',
              onTap: () {
                _showRoleChangeDialog(user, colorScheme);
              },
              color: colorScheme.tertiary,
            ),
            const SizedBox(height: 12),
            _buildDangerActionTile(
              icon: Icons.archive,
              title: 'Archive User',
              subtitle: 'Permanently archive this user account',
              onTap: () {
                _showArchiveDialog(user, colorScheme);
              },
              color: colorScheme.error,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String label,
    required String value,
    required ColorScheme colorScheme,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: colorScheme.onSurfaceVariant),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusTile({
    required IconData icon,
    required String label,
    required String value,
    required ColorScheme colorScheme,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: _getStatusColor(value)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    color: _getStatusColor(value),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDangerActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: color.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: color.withOpacity(0.7)),
          ],
        ),
      ),
    );
  }

  void _showRoleChangeDialog(UserModel user, ColorScheme colorScheme) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              user.userType == kAdminRole ? 'Downgrade User' : 'Promote User',
            ),
            content: Text(
              user.userType == kAdminRole
                  ? 'Are you sure you want to remove admin privileges from ${user.userName}?'
                  : 'Are you sure you want to grant admin privileges to ${user.userName}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _manageUserListBloc.add(
                    ManageUserListEvent.changeUserType(
                      userId: user.userId,
                      userType:
                          user.userType == kAdminRole
                              ? kPassengerRole
                              : kAdminRole, // Fixed the bug here
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                ),
                child: const Text('Confirm'),
              ),
            ],
          ),
    );
  }

  void _showArchiveDialog(UserModel user, ColorScheme colorScheme) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Archive User'),
            content: Text(
              'Are you sure you want to archive ${user.userName}? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _manageUserListBloc.add(
                    ManageUserListEvent.archiveUser(user.userId),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.error,
                ),
                child: const Text('Archive'),
              ),
            ],
          ),
    );
  }

  Color _getUserTypeColor(String userType) {
    switch (userType.toLowerCase()) {
      case 'passenger':
        return Colors.blue.shade700;
      case 'rider':
        return Colors.green.shade700;
      case 'admin':
        return Colors.deepPurple.shade700;
      default:
        return Colors.grey.shade700;
    }
  }

  IconData _getUserTypeIcon(String userType) {
    switch (userType.toLowerCase()) {
      case 'passenger':
        return Icons.person;
      case 'rider':
        return Icons.directions_car;
      case 'admin':
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green.shade600;
      case 'inactive':
        return Colors.red.shade600;
      case 'pending':
        return Colors.orange.shade600;
      case 'archived':
        return Colors.brown.shade600;
      default:
        return Colors.grey.shade600;
    }
  }
}
