import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider/get_pending_rider_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider_details/get_pending_rider_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/pending_riders_detail.dart';

class PendingRidersDetailPage extends StatefulWidget {
  const PendingRidersDetailPage({super.key, required this.userId});
  final int userId;

  @override
  State<PendingRidersDetailPage> createState() =>
      _PendingRidersDetailPageState();
}

class _PendingRidersDetailPageState extends State<PendingRidersDetailPage> {
  late GetPendingRiderDetailBloc _getPendingRiderDetailBloc;

  @override
  initState() {
    super.initState();
    _getPendingRiderDetailBloc =
        sl<GetPendingRiderDetailBloc>()
          ..add(GetPendingRiderDetailEvent.get(widget.userId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Pending Riders Detail')),
      body: BlocBuilder<GetPendingRiderDetailBloc, GetPendingRiderDetailState>(
        bloc: _getPendingRiderDetailBloc,
        builder: (context, state) {
          return state.maybeWhen(
            failure:
                (f) => ErrorWidgetWithRetry(
                  failure: f,
                  onRetry:
                      () => _getPendingRiderDetailBloc.add(
                        GetPendingRiderDetailEvent.get(widget.userId),
                      ),
                ),
            loaded: (rider) {
              return RiderDetailsWidget(rider: rider);
            },
            orElse: () => const Center(child: CircularProgressIndicator()),
          );
        },
      ),
    );
  }
}

class RiderDetailsWidget extends StatelessWidget {
  final RidersDetailsModel rider;

  const RiderDetailsWidget({super.key, required this.rider});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Section
          _buildProfileSection(context, theme, colorScheme),
          const SizedBox(height: 24),

          // Personal Information
          _buildSectionCard(
            context,
            theme,
            colorScheme,
            'Personal Information',
            Icons.person,
            [
              _buildInfoRow(context, 'Name', rider.userName),
              _buildInfoRow(context, 'Address', rider.userAddress),
              _buildInfoRow(context, 'Gender', rider.gender),
              _buildInfoRow(context, 'Email', rider.emailAddress),
              _buildInfoRow(context, 'Phone', rider.phoneNo),
              _buildInfoRow(context, 'User Type', rider.userType),
            ],
          ),

          // Account Information
          _buildSectionCard(
            context,
            theme,
            colorScheme,
            'Account Information',
            Icons.account_circle,
            [
              _buildInfoRow(context, 'User ID', rider.userId.toString()),
              _buildInfoRow(context, 'Login ID', rider.loginId),
              _buildInfoRow(context, 'Login Status', rider.loginStatus),
              _buildInfoRow(
                context,
                'Wallet Balance',
                '₹${rider.walletBalance.toStringAsFixed(2)}',
              ),
              _buildInfoRow(context, 'Registered Date', rider.registeredDate),
              _buildInfoRow(context, 'Registered Time', rider.registeredTime),
            ],
          ),

          // Vehicle Information
          _buildSectionCard(
            context,
            theme,
            colorScheme,
            'Vehicle Information',
            Icons.directions_car,
            [
              _buildInfoRow(
                context,
                'Vehicle Type ID',
                rider.vehicleTypeId.toString(),
              ),
              _buildInfoRow(context, 'Vehicle Number', rider.vehicleNo),
              _buildInfoRow(context, 'Owner Name', rider.ownerName),
              _buildInfoRow(context, 'Owner Phone', rider.ownerPhone),
            ],
          ),

          // Rider Status
          _buildSectionCard(
            context,
            theme,
            colorScheme,
            'Rider Status',
            Icons.verified_user,
            [
              _buildStatusRow(context, 'Available', rider.isRiderAvailable),
              _buildInfoRow(context, 'Applied Date', rider.riderAppliedDate),
              _buildInfoRow(context, 'Applied Time', rider.riderAppliedTime),
              _buildInfoRow(context, 'Verified Date', rider.riderVerifiedDate),
              _buildInfoRow(context, 'Verified Time', rider.riderVerifiedTime),
              _buildInfoRow(
                context,
                'Verified By',
                rider.riderVerifiedBy.toString(),
              ),
            ],
          ),

          // Location Information
          _buildSectionCard(
            context,
            theme,
            colorScheme,
            'Location Information',
            Icons.location_on,
            [
              _buildInfoRow(
                context,
                'Home Latitude',
                rider.homeLatitude.toString(),
              ),
              _buildInfoRow(
                context,
                'Home Longitude',
                rider.homeLongitude.toString(),
              ),
              _buildInfoRow(
                context,
                'Current Latitude',
                rider.currentLatitude.toString(),
              ),
              _buildInfoRow(
                context,
                'Current Longitude',
                rider.currentLongitude.toString(),
              ),
            ],
          ),

          // Documents Section
          _buildDocumentsSection(context, theme, colorScheme),

          const SizedBox(height: 24),

          // Action Buttons
          _buildActionButtons(context, theme, colorScheme, rider.userId),
        ],
      ),
    );
  }

  Widget _buildProfileSection(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      elevation: 2,
      color: colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40),
                border: Border.all(color: colorScheme.outline),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(40),
                child: ImageUtility.displayImageFromBase64(
                  rider.profilePicture,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    rider.userName,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    rider.emailAddress,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          rider.isRiderAvailable
                              ? colorScheme.primaryContainer
                              : colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      rider.isRiderAvailable ? 'Available' : 'Unavailable',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            rider.isRiderAvailable
                                ? colorScheme.onPrimaryContainer
                                : colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Card(
      elevation: 1,
      color: colorScheme.surface,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String? value) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return value == null
        ? SizedBox()
        : Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 120,
                child: Text(
                  '$label:',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  value.isEmpty ? 'N/A' : value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        );
  }

  Widget _buildStatusRow(BuildContext context, String label, bool status) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Icon(
                  status ? Icons.check_circle : Icons.cancel,
                  color: status ? colorScheme.primary : colorScheme.error,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  status ? 'Yes' : 'No',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: status ? colorScheme.primary : colorScheme.error,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsSection(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      elevation: 1,
      color: colorScheme.surface,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.folder, color: colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Documents',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDocumentGrid(context, theme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentGrid(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final documents = [
      {'title': 'Citizen Card Front', 'image': rider.riderCitizenCardFront},
      {'title': 'Citizen Card Back', 'image': rider.riderCitizenCardBack},
      {'title': 'Identity Photo', 'image': rider.riderIdentityPhoto},
      {'title': 'Vehicle Photo', 'image': rider.vehiclePhoto},
      {'title': 'Blue Book Photo', 'image': rider.blueBookPhoto},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        final doc = documents[index];
        return GestureDetector(
          onTap: () => _showImageDialog(context, doc['title']!, doc['image']!),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: colorScheme.outline),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: ImageUtility.displayImageFromBase64(
                        doc['image']!,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    doc['title']!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
    int userId,
  ) {
    ManageUserListBloc cudUserListBloc = sl<ManageUserListBloc>();
    return BlocListener<ManageUserListBloc, ManageUserListState>(
      bloc: cudUserListBloc,
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (msg) {
            AppLoadingDialog.hide(context);
            CustomToast.showSuccess(msg);
            sl<GetPendingRiderBloc>().add(
              GetPendingRiderEvent.removePendingListLocallyWhenAccepted(userId),
            );
            context.pop();
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      child: Row(
        children: [
          Expanded(
            child: CustomButtonPrimary(
              leadingIcon: Icon(Icons.check),
              title: 'Approve',
              onPressed: () => _onAcceptForRider(cudUserListBloc, userId),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: CustomButtonOutline(
              leadingIcon: Icon(Icons.close),
              title: 'Reject',
              onPressed:
                  () => _onRejectForBecomingRider(cudUserListBloc, userId),
            ),
          ),
        ],
      ),
    );
  }

  void _onRejectForBecomingRider(
    ManageUserListBloc cudUserListBloc,
    int userId,
  ) {
    cudUserListBloc.add(ManageUserListEvent.rejectForRider(userId: userId));
  }

  void _onAcceptForRider(ManageUserListBloc cudUserListBloc, int userId) {
    cudUserListBloc.add(ManageUserListEvent.acceptForRider(userId: userId));
  }

  void _showImageDialog(
    BuildContext context,
    String title,
    String base64Image,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: Text(title),
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                Flexible(
                  child: InteractiveViewer(
                    child: ImageUtility.displayImageFromBase64(
                      base64Image,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// Shows a confirmation dialog to the user, asking if they really want to
  /// perform the given [action] on a rider.
  ///
  /// The dialog will show a title of 'Approve Rider' or 'Reject Rider', and
  /// a content of 'Are you sure you want to approve/reject?'.
  ///
  /// The dialog will have two buttons: a [TextButton] with the text 'Cancel',
  /// and an [ElevatedButton] with the text [action].
  ///
  /// If the user presses the 'Cancel' button, the dialog will simply be
  /// dismissed.
  void _showConfirmationDialog(
    BuildContext context,
    String action,
    String message,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('$action Rider'),
            content: Text('Are you sure you want to $message?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Implement the actual approve/reject logic here
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Rider ${action.toLowerCase()}d successfully',
                      ),
                      backgroundColor:
                          action == 'Approve'
                              ? colorScheme.primary
                              : colorScheme.error,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      action == 'Approve'
                          ? colorScheme.primary
                          : colorScheme.error,
                  foregroundColor:
                      action == 'Approve'
                          ? colorScheme.onPrimary
                          : colorScheme.onError,
                ),
                child: Text(action),
              ),
            ],
          ),
    );
  }
}
