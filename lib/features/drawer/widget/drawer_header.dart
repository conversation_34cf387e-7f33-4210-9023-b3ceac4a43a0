import 'package:flutter/material.dart';
import '../../../core/utils/image_utils.dart';
import '../../../core/widget/custom_chip.dart';

class DrawerHeaderWidget extends StatelessWidget {
  const DrawerHeaderWidget({
    super.key,
    required this.textTheme,
    required this.name,
    this.imageUrl,
    required this.email,
    required this.rating,
    required this.chipTitle,
  });

  final TextTheme textTheme;
  final String name;
  final String? imageUrl;
  final String? email;
  final double rating;
  final String chipTitle;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 24),
      // Background color adapts to light/dark theme
      decoration: BoxDecoration(color: colors.surfaceContainerHighest),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Column(
                children: [
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: colors.surfaceContainerHighest,
                    child: ClipOval(
                      child: SizedBox(
                        width: 96,
                        height: 96,
                        child: ImageUtility.displayImageFromBase64(imageUrl),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      name,
                      style: textTheme.titleMedium?.copyWith(
                        color: colors.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 4),
                    email == null
                        ? SizedBox()
                        : Text(
                          email!,
                          style: textTheme.bodySmall?.copyWith(
                            color: colors.onSurfaceVariant.withOpacity(0.7),
                          ),
                        ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        Icon(Icons.star, color: colors.primary, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          rating.toStringAsFixed(1),
                          style: textTheme.bodySmall?.copyWith(
                            color: colors.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          CustomChipWidget(chipTitle: chipTitle),
        ],
      ),
    );
  }
}
