import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class DrawerWidget extends StatelessWidget {
  final String title;
  final IconData icons;
  final VoidCallback? onTap;

  const DrawerWidget({
    super.key,
    required this.title,
    required this.icons,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      horizontalTitleGap: 8,
      leading: Icon(
        icons,
        color: T.c.onSurfaceVariant.withOpacity(0.7), // subtle adaptive color
        size: 18,
      ),
      title: Text(title, style: T.t.titleLarge?.copyWith(color: T.c.onSurface)),
      onTap: onTap,
    );
  }
}
