import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/locale_cubit/locale_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/features/drawer/widget/setting_menu_tile.dart';

class SelectLanguagePage extends StatefulWidget {
  const SelectLanguagePage({super.key});

  @override
  State<SelectLanguagePage> createState() => _SelectLanguagePageState();
}

class _SelectLanguagePageState extends State<SelectLanguagePage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocaleCubit, Locale>(
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppBar(title: Text('Select Language')),
          body: Column(
            spacing: AppStyles.space16,
            children: [
              MenuTile(
                title: 'Default Language',
                subTitle: 'English',
                isShowTrailing: false,
                onTap: () {},
              ),
              MenuTile(
                title: 'English',
                subTitle: 'English',
                isShowTrailing: false,
                onTap: () {
                  sl<LocaleCubit>().changeLocale(Locale('en', 'US'));
                },
              ),
              MenuTile(
                title: 'नेपाली',
                subTitle: 'Nepali',
                isShowTrailing: false,
                onTap: () {
                  sl<LocaleCubit>().changeLocale(Locale('ne'));
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
