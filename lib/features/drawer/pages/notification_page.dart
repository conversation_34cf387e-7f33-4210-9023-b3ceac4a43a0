import 'package:flutter/material.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';

import '../../../core/widget/custom_switch.dart';
import '../widget/setting_menu_tile.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text('Notification Preferences')),
      body: Column(
        children: [
          MenuTile(
            title: 'Ride Requests',
            subTitle: 'Get notified about new ride requests',
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: false, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: 'Promotions',
            subTitle: 'Receive promotinal offers and bonuses',
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: true, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: 'Earnings',
            subTitle: 'Daily and weekly earnings summaries',
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: false, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: 'Safety',
            subTitle: 'Safety alerts and emergency notifications',
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: true, onChanged: (value) {}),
            ),
          ),
        ],
      ),
    );
  }
}
