import 'package:flutter/material.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import '../../../core/utils/url_launcher.dart';

class EmergencyAndSafety extends StatelessWidget {
  const EmergencyAndSafety({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text('Emergency & Safety')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Top buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTopButton(Icons.chat_bubble_outline, 'Support'),
                _buildTopButton(Icons.people_outline, 'Emergency contacts'),
              ],
            ),
            const SizedBox(height: 16),

            // Call 100 button
            CustomButtonPrimary(
              title: 'Call 100',
              fontSize: 18,
              fonwtWeight: FontWeight.bold,
              backgroundColor: ColorScheme.of(context).error,
              onPressed: () {
                urlLauncher(Uri(scheme: 'tel', path: '100'));
              },
              leadingIcon: Icon(LucideIcons.triangleAlert),
            ),

            const SizedBox(height: 20),

            // Section title
            const Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "How you're protected",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 12),

            // Grid of features
            Expanded(
              child: GridView.count(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                crossAxisCount: 2,
                mainAxisSpacing: 14,
                crossAxisSpacing: 12,
                childAspectRatio: 1,
                children: [
                  _buildProtectionCard(
                    'Proactive safety support',
                   ImageConstant.safateySupport,
                  ),
                  _buildProtectionCard(
                    'Drivers verification',
                   ImageConstant.driverVerification,
                   
                  ),
                  _buildProtectionCard(
                    'Protecting your privacy',
                   ImageConstant.safateySupport,
                    
                  ),
                  _buildProtectionCard(
                    'Staying safe on every ride',
                   ImageConstant.safateySupport,
                    
                  ),
                  _buildProtectionCard(
                    'Accidents: Steps to take',
                   ImageConstant.safateySupport,
                    
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopButton(IconData icon, String label) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 14),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: AppStyles.radiusMd,
        ),
        child: Column(
          children: [
            Icon(icon, size: 28),
            const SizedBox(height: 6),
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProtectionCard(String title, String imagePath) {
    return Container(
      decoration: BoxDecoration(),
      padding: const EdgeInsets.all(6),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Transform.rotate(
              angle: -0.2,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 61, 189, 56), // light green
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Image.asset(imagePath, height: 60),
              ),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
