import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/user_roles.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import '../../../core/router/app_route_names.dart';
import '../../../core/widget/custom_button.dart';
import '../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../widget/drawer_header.dart';
import '../widget/drawer_menu_widget.dart';
import '../widget/version_text.dart';

Widget buildPassengerAppDrawer(BuildContext context) {
  final dividerColor = T.c.onSurface.withOpacity(0.12);

  return SafeArea(
    child: Drawer(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.zero,
          bottomRight: Radius.zero,
        ),
      ),
      child: BlocConsumer<MyProfileBloc, MyProfileState>(
        listener: (context, state) {
          state.whenOrNull(
            failure: (failure) {
              CustomToast.showError(failure.message);
            },
          );
        },
        builder: (context, state) {
          return state.maybeWhen(
            orElse: () => const SizedBox(),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded:
                (data) => Column(
                  children: [
                    DrawerHeaderWidget(
                      textTheme: T.t,
                      name: data.userName,
                      imageUrl: data.profilePicture,
                      email: data.emailAddress,
                      rating: 4.8,
                      chipTitle: data.userType,
                    ),
                    Expanded(child: _buildDrawerBody(context, dividerColor)),
                  ],
                ),
          );
        },
      ),
    ),
  );
}

Widget _buildDrawerBody(BuildContext context, Color dividerColor) {
  return Column(
    children: [
      DrawerWidget(
        title: "Home",
        icons: LucideIcons.house,
        onTap: () {
          Navigator.of(context).pop();
        },
      ),
      DrawerWidget(
        title: "Your Trips",
        icons: LucideIcons.mapPin,
        onTap: () => context.pushNamed(AppRoutesName.passengerBookingTrips),
      ),
      // DrawerWidget(
      //   title: "Payment Methods",
      //   icons: LucideIcons.creditCard,
      //   onTap: () {
      //     context.pushNamed(AppRoutesName.paymentMethodPage);
      //   },
      // ),
      DrawerWidget(
        title: "Profile",
        icons: LucideIcons.user,
        onTap: () {
          context.pushNamed(AppRoutesName.profileScreen);
        },
      ),
      Divider(thickness: 1, color: dividerColor),
      DrawerWidget(
        title: "Settings",
        icons: LucideIcons.settings,
        onTap: () {
          context.pushNamed(AppRoutesName.setting);
        },
      ),
      // DrawerWidget(
      //   title: "Help Center",
      //   icons: LucideIcons.handHelping,
      //   onTap: () {},
      // ),
      Divider(thickness: 1, color: dividerColor),
      DrawerWidget(
        title: "Emergency & Safety",
        icons: LucideIcons.shieldCheck,
        onTap: () {
          context.pushNamed(AppRoutesName.emergencyAndSaftey);
        },
      ),
      const Spacer(),
      Padding(
        padding: const EdgeInsets.all(AppStyles.space12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppVersionText(),
            SizedBox(height: AppStyles.space4),
            BlocConsumer<LocalUserModeCubit, LocalUserModeState>(
              listener: (context, state) async {
                state.whenOrNull(
                  failure: (failure) {
                    AppLoadingDialog.hide(context);
                    CustomToast.showError(failure.message);
                  },
                  loading: () {
                    AppLoadingDialog.show(context);
                  },
                  loaded: (value) {
                    AppLoadingDialog.hide(context);
                    _navigateBasedOnRole(context, value);
                  },
                );
              },
              builder: (context, state) {
                return CustomButtonPrimary(
                  title: 'Driver Mode',
                  onPressed: () {
                    sl<LocalUserModeCubit>().switchDriverMode();
                  },
                );
              },
            ),
          ],
        ),
      ),
    ],
  );
}

void _navigateBasedOnRole(BuildContext context, String role) {
  sl<PassengerRouteBloc>().add(const PassengerRouteEvent.resetPassengerRoute());

  if (role == kPendingRequestForRiderRole) {
    context.pushNamed(AppRoutesName.documentReviewPage);
  } else if (role == kRiderRole) {
    context.goNamed(AppRoutesName.driverHome);
  } else {
    context.pushNamed(AppRoutesName.whatsYourNamePage);
  }
}
