import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/features/my_profile/bloc/terminate_account/terminate_account_bloc.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/widget/custom_appbar.dart';
import '../../../core/widget/custom_dilog_alert_box.dart';
import '../widget/setting_menu_tile.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text(L.t.settingScreenAppBarTitle)),
      body: Column(
        spacing: AppStyles.space12,
        children: [
          MenuTile(
            title: 'Theme Mode',
            trailing: BlocBuilder<AppThemeCubit, AppThemeState>(
              builder: (context, tState) {
                return DropdownButton<ThemeMode>(
                  value: tState.mode,
                  underline: SizedBox(),
                  onChanged: (mode) {
                    if (mode != null) {
                      sl<AppThemeCubit>().setTheme(mode);
                    }
                  },
                  items: const [
                    DropdownMenuItem(
                      value: ThemeMode.system,
                      child: Text('System'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.light,
                      child: Text('Light'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.dark,
                      child: Text('Dark'),
                    ),
                  ],
                );
              },
            ),
          ),
          MenuTile(
            title: L.t.settingScreenLanguageTitle,
            subTitle: L.t.settingScreenLanguageSubtitle,
            onTap: () {
              context.pushNamed(AppRoutesName.selectLanguage);
            },
          ),
          MenuTile(
            title: L.t.settingScreenChangePasswordTitle,
            // subTitle: 'English',
            onTap: () {
              context.pushNamed(AppRoutesName.changePassword);
            },
          ),
          MenuTile(
            title: L.t.settingScreenNotificationTitle,
            onTap: () {
              context.pushNamed(AppRoutesName.notificationPage);
            },
          ),
          MenuTile(title:L.t.settingScreenRulesAndTermsTitle, onTap: () {}),
          MenuTile(title: L.t.settingScreenLogoutTitle, onTap: () {}),
          BlocConsumer<TerminateAccountBloc, TerminateAccountState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () => AppLoadingDialog.show(context),
                failure: (failure) {
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showSuccess(data);
                  CacheService.instance.clearTokenData();
                  context.goNamed(AppRoutesName.signIn);
                },
              );
            },
            builder: (context, state) {
              return MenuTile(
                title: L.t.settingScreenDeleteAccountTitle,
                textColor: ColorScheme.of(context).error,
                isShowTrailing: false,
                onTap: () {
                  _onDeleteAccountTap(context);
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

void _onDeleteAccountTap(BuildContext context) async {
  final confirmed = await showConfirmationDialog(
    context: context,
    title: L.t.settingScreenDeleteDialogTitle,
    message:
       L.t.settingScreenDeleteDialogMessage,
    confirmText: L.t.settingScreenDeleteDialogConfirmText,
    cancelText: L.t.settingScreenDeleteDialogCancelText,
    icon: Icons.warning_amber_rounded,
    iconColor: Colors.red,
    confirmButtonColor: Colors.red,
    cancelTextColor: Colors.grey,
  );

  if (confirmed == true) {
    sl<TerminateAccountBloc>().add(TerminateAccountEvent.terminateAccount());
  }
}
