import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/my_profile/bloc/terminate_account/terminate_account_bloc.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/widget/custom_appbar.dart';
import '../../../core/widget/custom_dilog_alert_box.dart';
import '../widget/setting_menu_tile.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text('Settings')),
      body: Column(
        spacing: AppStyles.space12,
        children: [
          MenuTile(
            title: 'Theme Mode',
            trailing: BlocBuilder<AppThemeCubit, AppThemeState>(
              builder: (context, tState) {
                return DropdownButton<ThemeMode>(
                  value: tState.mode,
                  underline: SizedBox(),
                  onChanged: (mode) {
                    if (mode != null) {
                      sl<AppThemeCubit>().setTheme(mode);
                    }
                  },
                  items: const [
                    DropdownMenuItem(
                      value: ThemeMode.system,
                      child: Text('System'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.light,
                      child: Text('Light'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.dark,
                      child: Text('Dark'),
                    ),
                  ],
                );
              },
            ),
          ),
          MenuTile(
            title: 'Language',
            subTitle: 'English',
            onTap: () {
              context.pushNamed(AppRoutesName.selectLanguage);
            },
          ),
          MenuTile(
            title: 'Change Password',
            // subTitle: 'English',
            onTap: () {
              context.pushNamed(AppRoutesName.changePassword);
            },
          ),
          MenuTile(
            title: 'Notification',
            onTap: () {
              context.pushNamed(AppRoutesName.notificationPage);
            },
          ),
          MenuTile(title: 'Rules and terms', onTap: () {}),
          MenuTile(title: 'Logout', onTap: () {}),
          BlocConsumer<TerminateAccountBloc, TerminateAccountState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () => AppLoadingDialog.show(context),
                failure: (failure) {
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showSuccess(data);
                  CacheService.instance.clearTokenData();
                  context.goNamed(AppRoutesName.signIn);
                },
              );
            },
            builder: (context, state) {
              return MenuTile(
                title: 'Delete Account',
                textColor: ColorScheme.of(context).error,
                isShowTrailing: false,
                onTap: () {
                  _onDeleteAccountTap(context);
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

void _onDeleteAccountTap(BuildContext context) async {
  final confirmed = await showConfirmationDialog(
    context: context,
    title: 'Delete Account',
    message:
        'This action will permanently delete your account and all data associated with it. Are you sure you want to proceed?',
    confirmText: 'Delete Account',
    cancelText: 'Cancel',
    icon: Icons.warning_amber_rounded,
    iconColor: Colors.red,
    confirmButtonColor: Colors.red,
    cancelTextColor: Colors.grey,
  );

  if (confirmed == true) {
    sl<TerminateAccountBloc>().add(TerminateAccountEvent.terminateAccount());
  }
}
