
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';

abstract interface class RequestForDriverRepository {
  FutureEither<String> requestForDriver(
    DriverRegistrationRequest driverRequestForm,
  );
}

class RequestForDriverRepositoryI implements RequestForDriverRepository {
  final ApiService _apiService;
  RequestForDriverRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  @override
  FutureEither<String> requestForDriver(
    DriverRegistrationRequest driverRequestForm,
  ) async {
    final data = await driverRequestForm.serverToMap();
    // log(jsonEncode(data));
    return await _apiService.post<String>(
      'MyProfile/RequestForRider',
      data: data,
      fromJson: (data) => "Success",
    );
    // return Left(UnexpectedFailure(message: "Failed to request for driver"));
  }
}
