import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';

class DriverRegistrationRequest {
  final String? userName;
  final String? phoneNo;
  final String? gender;
  final String? emailAddress;
  final String? address;
  final int? vehicleTypeId;
  final String? vehicleNo;
  final String? ownerName;
  final String? ownerPhone;

  final XFile? citizenCardFront;
  final XFile? citizenCardBack;
  final XFile? identityPhoto;
  final XFile? vehiclePhoto;
  final XFile? blueBookPhoto;

  DriverRegistrationRequest({
    this.userName,
    this.phoneNo,
    this.gender,
    this.emailAddress,
    this.address,
    this.vehicleTypeId,
    this.vehicleNo,
    this.ownerName,
    this.ownerPhone,
    this.citizenCardFront,
    this.citizenCardBack,
    this.identityPhoto,
    this.vehiclePhoto,
    this.blueBookPhoto,
  });

  /// Converts to map ready to send to server (base64 encoded files)
  Future<Map<String, dynamic>> serverToMap() async {
    return {
      'UserName': userName,
      'PhoneNo': phoneNo,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'UserAddress': address,
      'VehicleTypeId': vehicleTypeId,
      'VehicleNo': vehicleNo,
      'OwnerName': ownerName,
      'OwnerPhone': ownerPhone,
      'RiderCitizenCardFront': await _fileToMap(citizenCardFront),
      'RiderCitizenCardBack': await _fileToMap(citizenCardBack),
      'RiderIdentityPhoto': await _fileToMap(identityPhoto),
      'VehiclePhoto': await _fileToMap(vehiclePhoto),
      'BlueBookPhoto': await _fileToMap(blueBookPhoto),
    };
  }

  /// Converts to map to store locally (just file paths)
  Map<String, dynamic> localToMap() {
    return {
      'UserName': userName,
      'PhoneNo': phoneNo,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'UserAddress': address,
      'VehicleTypeId': vehicleTypeId,
      'VehicleNo': vehicleNo,
      'OwnerName': ownerName,
      'OwnerPhone': ownerPhone,
      'CitizenCardFrontPath': citizenCardFront?.path,
      'CitizenCardBackPath': citizenCardBack?.path,
      'IdentityPhotoPath': identityPhoto?.path,
      'VehiclePhotoPath': vehiclePhoto?.path,
      'BlueBookPhotoPath': blueBookPhoto?.path,
    };
  }

  /// Factory to create from local storage (paths)
  factory DriverRegistrationRequest.fromMap(Map<String, dynamic> json) {
    return DriverRegistrationRequest(
      userName: json['UserName'] as String?,
      phoneNo: json['PhoneNo'] as String?,
      gender: json['Gender'] as String?,
      emailAddress: json['EmailAddress'] as String?,
      address: json['UserAddress'] as String?,
      vehicleTypeId: json['VehicleTypeId'],
      vehicleNo: json['VehicleNo'] as String?,
      ownerName: json['OwnerName'] as String?,
      ownerPhone: json['OwnerPhone'] as String?,
      citizenCardFront: _pathToXFile(json['CitizenCardFrontPath']),
      citizenCardBack: _pathToXFile(json['CitizenCardBackPath']),
      identityPhoto: _pathToXFile(json['IdentityPhotoPath']),
      vehiclePhoto: _pathToXFile(json['VehiclePhotoPath']),
      blueBookPhoto: _pathToXFile(json['BlueBookPhotoPath']),
    );
  }

  /// Converts a file path string to XFile, if valid
  static XFile? _pathToXFile(dynamic path) {
    if (path is String && path.isNotEmpty) {
      return XFile(path);
    }
    return null;
  }

  /// For immutability
  DriverRegistrationRequest copyWith({
    String? userName,
    String? phoneNo,
    String? gender,
    String? emailAddress,
    String? address,
    int? vehicleTypeId,
    String? vehicleNo,
    String? ownerName,
    String? ownerPhone,
    XFile? citizenCardFront,
    XFile? citizenCardBack,
    XFile? identityPhoto,
    XFile? vehiclePhoto,
    XFile? blueBookPhoto,
  }) {
    return DriverRegistrationRequest(
      userName: userName ?? this.userName,
      phoneNo: phoneNo ?? this.phoneNo,
      gender: gender ?? this.gender,
      emailAddress: emailAddress ?? this.emailAddress,
      address: address ?? this.address,
      vehicleTypeId: vehicleTypeId ?? this.vehicleTypeId,
      vehicleNo: vehicleNo ?? this.vehicleNo,
      ownerName: ownerName ?? this.ownerName,
      ownerPhone: ownerPhone ?? this.ownerPhone,
      citizenCardFront: citizenCardFront ?? this.citizenCardFront,
      citizenCardBack: citizenCardBack ?? this.citizenCardBack,
      identityPhoto: identityPhoto ?? this.identityPhoto,
      vehiclePhoto: vehiclePhoto ?? this.vehiclePhoto,
      blueBookPhoto: blueBookPhoto ?? this.blueBookPhoto,
    );
  }

  /// Converts XFile to base64 and filename
  Future<Map<String, String>> _fileToMap(XFile? file) async {
    if (file == null) {
      throw Exception('File is null');

      // return {'FileData': '', 'ClientFileName': ''};
    }

    final base64String = await ImageUtility.getCompressedBase64(
      file,
      maxSizeKB: 20,
    );
    if (base64String == null) {
      throw Exception('Failed to compress image');
    }
    final image = await ImageUtility.getCompressedImageDataBase64WithMimeType(
      file,
    );
    return {'FileData': image!, 'ClientFileName': file.name};
  }
}
