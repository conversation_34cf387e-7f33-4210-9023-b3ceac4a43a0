import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../../../../core/router/app_route_names.dart';
import '../../../../core/theme/app_styles.dart';
import '../../../../core/widget/custom_button.dart';
import '../../../../core/widget/custom_pop_button.dart';

class SelfiePage extends StatefulWidget {
  const SelfiePage({super.key});

  @override
  State<SelfiePage> createState() => _SelfiePageState();
}

class _SelfiePageState extends State<SelfiePage> {
  XFile? _identityPhoto;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: AppStyles.space12,
          vertical: AppStyles.space40,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomBackButton(),
            SizedBox(height: AppStyles.space8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30.0),
              child: Column(
                children: [
                  Text(
                    'Upload your Selfie',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.displayLarge,
                  ),
                ],
              ),
            ),
            SizedBox(height: AppStyles.space12),
            _buildImagePicker(
              label: 'Select Identity Photo (Selfie)',
              image: _identityPhoto,
              onTap: _pickIdentityPhoto,
              placeholderImage: ImageConstant.selfieLottie,
            ),
            const SizedBox(height: 24),
            BlocListener<RequestForDriverBloc, RequestForDriverState>(
              listener: (context, state) {
                state.whenOrNull(
                  loading: () => AppLoadingDialog.show(context),
                  loaded: (data) async {
                    CoreLocalDataSource local = CoreLocalDataSource();
                    await local.clear(AppCachedKeys.requestForDriverForm);

                    if (context.mounted) {
                      AppLoadingDialog.hide(context);
                      sl<MyProfileBloc>().add(MyProfileEvent.get(true));
                      context.goNamed(AppRoutesName.documentReviewPage);
                    }
                  },
                  failure: (failure) {
                    AppLoadingDialog.hide(context);
                    CustomToast.showError(failure.message);
                  },
                );
              },
              child: CustomButtonPrimary(title: 'Next', onPressed: _onPressed),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePicker({
    String? label,
    required XFile? image,
    required VoidCallback onTap,
    required String placeholderImage,
  }) {
    return Material(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10), // Added for consistency
        child: Container(
          height: 500,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(10),
          ),
          clipBehavior: Clip.antiAlias,
          child:
              image != null
                  ? Image.file(
                    File(image.path),
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 500,
                  )
                  : Center(
                    child: Lottie.asset(
                      placeholderImage,
                      width: 400,
                      height: 400,
                      fit: BoxFit.contain,
                    ),
                  ),
        ),
      ),
    );
  }

  Future<void> _pickIdentityPhoto() async {
    final pickedFile = await _pickImage();
    if (pickedFile != null) {
      setState(() {
        _identityPhoto = pickedFile;
      });
    }
  }

  Future<XFile?> _pickImage() async {
    final picker = ImagePicker();

    ///TODO:: need to change with camera
    return await picker.pickImage(source: ImageSource.gallery);
  }

  void _onPressed() {
    if (_identityPhoto == null) {
      CustomToast.showError('Please upload your selfie first.');
      return;
    }

    sl<RequestForDriverBloc>().add(
      RequestForDriverEvent.requestForDriver(_identityPhoto!),
    );
  }
}
