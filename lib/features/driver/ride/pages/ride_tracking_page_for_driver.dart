import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/utils/url_launcher.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/convert_map_cart_to_markers.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'dart:async';
import 'dart:math';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/ride_map_helper.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/polylines_points/polylines_points_bloc.dart';
import 'package:safari_yatri/features/driver/home/<USER>/ride_request_list_widget.dart';
import 'package:safari_yatri/features/driver/ride/ride_start_completor_bloc/ride_start_completor_bloc.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

class RideTrackingPageForDriver extends StatefulWidget {
  final NewPassengerModel newPassengerModel;
  final BookingModel bookingModel;

  const RideTrackingPageForDriver({
    super.key,
    required this.newPassengerModel,
    required this.bookingModel,
  });

  @override
  State<RideTrackingPageForDriver> createState() =>
      _RideTrackingPageForDriverState();
}

class _RideTrackingPageForDriverState extends State<RideTrackingPageForDriver> {
  late NewPassengerModel newPassengerModel;

  GoogleMapController? _mapController;

  Set<Marker> _driverCurrentLocationToPassengerPickupMarkers = {};
  Set<Marker> _passengerPickupToDestinationMarkers = {};

  Set<Polyline> _pickupToDestinationPolyline = {};
  Set<Polyline> _currentToPickupPolyline = {};
  late final CurrentLocationBloc _currentLocationBloc;
  late final RideStartCompletorBloc _rideStartCompletorBloc;
  LatLng? _currentLocation;
  bool _isLoadingBtnState = false;
  RideStartCompletorButtonState _trackPreviousRideStartCompletorButtonState =
      RideStartCompletorButtonState.imHere;
  @override
  void initState() {
    super.initState();
    newPassengerModel = widget.newPassengerModel;
    final uniqueValue =
        "${newPassengerModel.passengerName}${newPassengerModel.bookingStartTime}${newPassengerModel.bookingStartTime}";
    sl<PolylinesPointsBloc>().add(
      PolylinesPointsEvent.getPolylines(
        locations: newPassengerModel.cartDetailViews,
        uniqueValue: uniqueValue,
      ),
    );

    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.stop());
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());

    _rideStartCompletorBloc = sl<RideStartCompletorBloc>();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<CurrentLocationBloc, CurrentLocationState>(
          bloc: _currentLocationBloc,
          builder: (context, currentLocationState) {
            currentLocationState.whenOrNull(
              loaded: (data) {
                _currentLocation = LatLng(data.latitude, data.longitude);
              },
            );
            return BlocConsumer<PolylinesPointsBloc, PolylinesPointsState>(
              listener: (context, state) {
                _polylineListener(state);
              },
              builder: (context, state) {
                return _buildLoadedData(bottomInset, context);
              },
            );
          },
        ),
      ),
    );
  }

  void _polylineListener(PolylinesPointsState state) {
    state.whenOrNull(
      loaded: (data) async {
        _pickupToDestinationPolyline = AppMapHelper.createPolyline(
          data.polylinePoints,
          width: 8,
        );
        _currentToPickupPolyline = AppMapHelper.createPolyline(
          data.polylinePointsFromCurrentToStart ?? [],
          color: Colors.red,
          polylineId: "currentToPickup",
          width: 6,
        );

        _driverCurrentLocationToPassengerPickupMarkers =
            await AppMapHelper.generateMarkerCurrentDriverToPassengerPickup([
              _currentLocation!,
              convertCartDetailsToLatLng(
                newPassengerModel.cartDetailViews,
              ).first,
            ]);

        _passengerPickupToDestinationMarkers =
            await AppMapHelper.generateRouteMarkers(
              convertCartDetailsToLatLng(newPassengerModel.cartDetailViews),
            );

        if (mounted) {
          setState(() {});
        }

        if (_mapController != null) {
          _zoomToFitMarkers(
            _currentLocation == null
                ? []
                : [_currentLocation!, ...data.polylinePoints],
          );
        }
      },
    );
  }

  Padding _buildLoadedData(double bottomInset, BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: bottomInset),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 5,
            child: Stack(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.72,
                  child: BlocConsumer<
                    RideStartCompletorBloc,
                    RideStartCompletorState
                  >(
                    bloc: _rideStartCompletorBloc,
                    listener: (context, state) {
                      state.whenOrNull(
                        loaded: (data) {
                          if (data == RideStartCompletorButtonState.completed) {
                            context.goNamed(AppRoutesName.ratePassengerPage);
                            return;
                          }
                        },
                        failure:
                            (failure) => CustomToast.showError(failure.message),
                      );
                    },
                    builder: (context, state) {
                      RideStartCompletorButtonState btnState = state.maybeWhen(
                        loading: () {
                          _isLoadingBtnState = true;
                          return RideStartCompletorButtonState.imHere;
                        },
                        loaded: (data) {
                          _trackPreviousRideStartCompletorButtonState = data;

                          _isLoadingBtnState = false;
                          return data;
                        },
                        orElse: () {
                          _isLoadingBtnState = false;
                          return _trackPreviousRideStartCompletorButtonState;
                        },
                      );
                      return CustomGoogleMap(
                        polylines:
                            _isLoadingBtnState
                                ? {}
                                : btnState ==
                                    RideStartCompletorButtonState.imHere
                                ? _currentToPickupPolyline
                                : _pickupToDestinationPolyline,
                        markers:
                            _isLoadingBtnState
                                ? {}
                                : btnState ==
                                    RideStartCompletorButtonState.imHere
                                ? _driverCurrentLocationToPassengerPickupMarkers
                                : _passengerPickupToDestinationMarkers,
                        initialCameraPosition: CameraPosition(
                          target: LatLng(
                            newPassengerModel
                                .cartDetailViews
                                .first
                                .sourceLatitude,
                            newPassengerModel
                                .cartDetailViews
                                .first
                                .sourceLongitude,
                          ),
                          zoom: kMapInitialZoom,
                        ),
                        onMapCreated: (mapController) {
                          _mapController = mapController;
                        },
                        myLocationEnabled: true,
                      );
                    },
                  ),
                ),

                Positioned(
                  bottom: 0,
                  left: 12,

                  child: FilledButton.icon(
                    icon: Icon(Icons.navigation),
                    onPressed: _onPressNavigator,
                    label: Text("Navigator"),
                  ),
                ),
              ],
            ),
          ),
          Flexible(
            flex: 2,
            child: Container(
              height: 300,
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
                boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 8)],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 8),
                  RideRequestItem(
                    newPassenger: widget.newPassengerModel,
                    showPhoneIcon: true,
                    phoneIconOnTap: _onPhoneIconOnTap,
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: BlocBuilder<
                      RideStartCompletorBloc,
                      RideStartCompletorState
                    >(
                      bloc: _rideStartCompletorBloc,
                      builder: (context, state) {
                        RideStartCompletorButtonState buttonState = state
                            .maybeWhen(
                              loaded: (data) => data,
                              orElse:
                                  () => RideStartCompletorButtonState.imHere,
                            );

                        bool isLoading = state.maybeWhen(
                          loading: () => true,
                          orElse: () => false,
                        );
                        return CustomButtonPrimary(
                          isLoading: isLoading,
                          onPressed:
                              buttonState ==
                                      RideStartCompletorButtonState.completed
                                  ? null
                                  : isLoading
                                  ? null
                                  : () => _onPressedButton(
                                    buttonState,
                                    widget.bookingModel.bookingId,
                                  ),
                          title: getButtonTextFromEnum(buttonState),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onPhoneIconOnTap() {
    if (newPassengerModel.passengerPhone.isEmpty) {
      CustomToast.showError("No phone number available");
      return;
    }
    urlLauncher(Uri(scheme: 'tel', path: newPassengerModel.passengerPhone));
  }

  void _onPressNavigator() {
    if (_currentLocation == null) return;

    List<LatLng> latlngs = convertCartDetailsToLatLng(
      widget.newPassengerModel.cartDetailViews,
    );
    if (latlngs.isEmpty) return;

    final passengerPickupLocation = latlngs.first;

    // Create base query parameters
    final queryParameters = <String, String>{
      'q':
          '${passengerPickupLocation.latitude},${passengerPickupLocation.longitude}',
      'mode': 'd',
    };

    // Conditionally add destination or origin
    if (_trackPreviousRideStartCompletorButtonState !=
        RideStartCompletorButtonState.imHere) {
      queryParameters['destination'] =
          '${latlngs.last.latitude},${latlngs.last.longitude}';
    } else {
      queryParameters['origin'] =
          '${_currentLocation!.latitude},${_currentLocation!.longitude}';
    }

    // Recreate Uri with query parameters
    final navigationIntent = Uri(
      scheme: 'google.navigation',
      queryParameters: queryParameters,
    );

    urlLauncher(navigationIntent);
  }

  Future<void> _zoomToFitMarkers(List<LatLng> locations) async {
    if (_mapController == null || locations.isEmpty || !mounted) {
      return;
    }

    await Future.delayed(const Duration(milliseconds: 200));
    if (!mounted) return;

    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = min(minLat, location.latitude);
      maxLat = max(maxLat, location.latitude);
      minLng = min(minLng, location.longitude);
      maxLng = max(maxLng, location.longitude);
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, kMapPadding + 40),
    );
  }

  String getButtonTextFromEnum(RideStartCompletorButtonState buttonState) {
    switch (buttonState) {
      case RideStartCompletorButtonState.imHere:
        return "I'm here";
      case RideStartCompletorButtonState.start:
        return "Start Ride";
      case RideStartCompletorButtonState.complete:
        return "Complete Ride";

      case RideStartCompletorButtonState.completed:
        return "Ride Completed";
    }
  }

  void _onPressedButton(
    RideStartCompletorButtonState buttonState,
    int bookingId,
  ) {
    switch (buttonState) {
      case RideStartCompletorButtonState.imHere:
        _rideStartCompletorBloc.add(
          const RideStartCompletorEvent.originToPickup(),
        );
        break;
      case RideStartCompletorButtonState.start:
        _rideStartCompletorBloc.add(
          RideStartCompletorEvent.startRide(bookingId),
        );
        break;
      case RideStartCompletorButtonState.complete:
        _rideStartCompletorBloc.add(
          RideStartCompletorEvent.completeRide(bookingId),
        );
        break;

      case RideStartCompletorButtonState.completed:
        return;
    }
  }
}
