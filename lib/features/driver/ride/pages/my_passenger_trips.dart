import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/booking_trip_widget.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_passengers_booking.dart/get_my_passengers_booking_bloc.dart';

class MyPassengerTripsPage extends StatefulWidget {
  const MyPassengerTripsPage({super.key});

  @override
  State<MyPassengerTripsPage> createState() => _MyPassengerTripsPageState();
}

class _MyPassengerTripsPageState extends State<MyPassengerTripsPage> {
  late final GetMyPassengersBookingBloc _getMyPassengerBloc;
  @override
  void initState() {
    super.initState();
    _getMyPassengerBloc =
        sl<GetMyPassengersBookingBloc>()
          ..add(GetMyPassengersBookingEvent.getActiveBookings());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text("My Trips")),

      body:
          BlocBuilder<GetMyPassengersBookingBloc, GetMyPassengersBookingState>(
            builder: (context, state) {
              return state.when(
                initial: () => SizedBox(),
                loading: () => Center(child: CircularProgressIndicator()),
                loaded: (data) {
                  return RefreshIndicator(
                    onRefresh:
                        () async => _getMyPassengerBloc.add(
                          GetMyPassengersBookingEvent.getActiveBookings(
                            refresh: true,
                          ),
                        ),
                    child: ListView.builder(
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        return TripBookingCart(booking: data[index]);
                      },
                    ),
                  );
                },
                failure:
                    (failure) => ErrorWidgetWithRetry(
                      failure: failure,
                      onRetry:
                          () => _getMyPassengerBloc.add(
                            GetMyPassengersBookingEvent.getActiveBookings(
                              refresh: true,
                            ),
                          ),
                    ),
              );
            },
          ),
    );
  }
}
