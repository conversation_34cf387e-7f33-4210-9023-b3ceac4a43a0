import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'ride_start_completor_event.dart';
part 'ride_start_completor_state.dart';
part 'ride_start_completor_bloc.freezed.dart';

class RideStartCompletorBloc
    extends Bloc<RideStartCompletorEvent, RideStartCompletorState> {
  final BookingRepository _bookingRepository;
  RideStartCompletorBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(
        RideStartCompletorState.loaded(RideStartCompletorButtonState.imHere),
      ) {
    on<_OriginToPickup>(_onOriginToPickup);
    on<_StartRide>(_onStartRide);
    on<_CompleteRide>(_onCompleteRide);
  }
  Future<void> _onStartRide(
    _StartRide event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    emit(RideStartCompletorState.loading());
    final result = await _bookingRepository.serviceStart(event.bookingId);

    result.fold(
      (left) => emit(RideStartCompletorState.failure(left)),
      (right) => emit(
        RideStartCompletorState.loaded(RideStartCompletorButtonState.complete),
      ),
    );
  }

  Future<void> _onCompleteRide(
    _CompleteRide event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    emit(RideStartCompletorState.loading());
    final result = await _bookingRepository.serviceComplete(event.bookingId);

    result.fold(
      (left) => emit(RideStartCompletorState.failure(left)),
      (right) => emit(
        RideStartCompletorState.loaded(RideStartCompletorButtonState.completed),
      ),
    );
  }

  Future<void> _onOriginToPickup(
    _OriginToPickup event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    await Future.delayed(const Duration(seconds: 1));
    emit(RideStartCompletorState.loaded(RideStartCompletorButtonState.start));
  }
}
