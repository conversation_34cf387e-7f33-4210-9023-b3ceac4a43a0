// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_passengers_booking/get_my_current_passengers_booking_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';

class RiderWaitingForPassengerAcceptDialogPage extends StatefulWidget {
  const RiderWaitingForPassengerAcceptDialogPage({
    super.key,
    required this.passengerModel,
  });

  final NewPassengerModel passengerModel;

  @override
  State<RiderWaitingForPassengerAcceptDialogPage> createState() =>
      _RiderWaitingForPassengerAcceptDialogPageState();
}

class _RiderWaitingForPassengerAcceptDialogPageState
    extends State<RiderWaitingForPassengerAcceptDialogPage> {
  late GetMyCurrentPassengerBooking getMyPassengersBookingBloc;
  Timer? _closeTimer;
  Timer? _countdownTimer;
  static const int _dialogAutoClosingInSecondsIfRequestNotAccepted = 60;
  int _remainingSeconds = _dialogAutoClosingInSecondsIfRequestNotAccepted;

  ///unique value le hamilaii kun chaii user ko maa janu parney ho ta
  ///vanera batalauxa hamisanga passengerId aayera yesto gareko plus
  ///routes polyline haru tehii anushar ko dekhaune without fetching
  late final String uniqueValue;
  @override
  void initState() {
    super.initState();

    uniqueValue =
        "${widget.passengerModel.passengerName}${widget.passengerModel.totalDistanceInMeter}";
    getMyPassengersBookingBloc = sl<GetMyCurrentPassengerBooking>();
    getMyPassengersBookingBloc.add(
      GetMyCurrentPassengersBookingEvent.resumeCurrentBookign(),
    );

    // Auto-close timer
    _closeTimer = Timer(
      const Duration(seconds: _dialogAutoClosingInSecondsIfRequestNotAccepted),
      () {
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
          CustomToast.showError("Passenger did not accept your request");
        }
      },
    );

    // Countdown timer
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _closeTimer?.cancel();
    _countdownTimer?.cancel();
    getMyPassengersBookingBloc.add(
      GetMyCurrentPassengersBookingEvent.stopCurrentBookings(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      child: BlocListener<
        GetMyCurrentPassengerBooking,
        GetMyCurrentPassengersBookingState
      >(
        listener: (context, state) {
          _myPassengerListener(state, context);
        },
        child: AlertDialog(
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 20,
          ),
          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          titlePadding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
          title: Center(
            child: Column(
              children: [
                const SizedBox.square(
                  dimension: 30,
                  child: CircularProgressIndicator(),
                ),
                const SizedBox(height: 8),
                Text(
                  "Waiting...",
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.passengerModel.cartDetailViews.first.sourceAddress,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                widget.passengerModel.cartDetailViews.last.destinationAddress,
                style: TextStyle(color: Colors.grey[700], fontSize: 13),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    widget.passengerModel.passengerName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(width: 4),
                  const Icon(Icons.star, size: 14, color: Colors.orange),
                  const Spacer(),
                  Text(
                    widget.passengerModel.bookingStartTime,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color.fromARGB(255, 195, 144, 144),
                    ),
                  ),
                  // Text(
                  //   widget.passengerModel.passengerPhone,
                  //   style: const TextStyle(fontSize: 12, color: Colors.grey),
                  // ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    CurrencyFormatter.format(
                      widget.passengerModel.passengerFareAmount,
                    ),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    "${widget.passengerModel.totalDistanceInMeter} Meter",
                    style: TextStyle(color: Colors.grey[700], fontSize: 13),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Null _myPassengerListener(
    GetMyCurrentPassengersBookingState state,
    BuildContext context,
  ) {
    return state.whenOrNull(
      loaded: (data) {
        if (data.isOnGoingBooking && data.myPassengersBooking.isNotEmpty) {
          for (int i = 0; i < data.myPassengersBooking.length; i++) {
            final localUniqueValue =
                "${data.myPassengersBooking[i].passengerName}${data.myPassengersBooking[i].totalDistanceInMeter}";
            if (localUniqueValue.toLowerCase() == uniqueValue.toLowerCase()) {
              __onSuccess(context, data.myPassengersBooking[i]);
              break;
            }
          }
        }
      },
    );
  }

  void __onSuccess(BuildContext context, BookingModel bookingModel) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
    CustomToast.showSuccess("Passenger accepted your offer");

    context.pushNamed(
      AppRoutesName.rideTrackingPageForDriver,
      extra: {
        'newPassengerModel': widget.passengerModel,
        'bookingModel': bookingModel,
      },
    );
  }
}
