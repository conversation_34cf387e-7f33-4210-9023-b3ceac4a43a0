// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accept_rider_request_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AcceptRiderRequestEvent {
  int get riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AcceptRiderRequestEvent &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent(riderId: $riderId)';
  }
}

/// @nodoc

class _Accept implements AcceptRiderRequestEvent {
  const _Accept(this.riderId);

  @override
  final int riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Accept &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent.accept(riderId: $riderId)';
  }
}

/// @nodoc

class _Decline implements AcceptRiderRequestEvent {
  const _Decline(this.riderId);

  @override
  final int riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Decline &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent.decline(riderId: $riderId)';
  }
}
