import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_current_booking_event.dart';
part 'get_my_current_booking_state.dart';
part 'get_my_current_booking_bloc.freezed.dart';

class GetMyCurrentBookingBloc
    extends Bloc<GetMyCurrentBookingEvent, GetMyCurrentBookingState> {
  final BookingRepository _bookingRepository;
  final PollingService _pollingService;
  final String _pollingId = "GetMyCurrentBookingBloc";
  BookingModel? _booking;
  GetMyCurrentBookingBloc({
    required BookingRepository repo,
    required PollingService polling,
  }) : _bookingRepository = repo,
       _pollingService = polling,
       super(GetMyCurrentBookingState.initial()) {
    if (!_pollingService.hasTask(_pollingId)) {
      _pollingService.register(
        id: _pollingId,
        immediate: false,
        onPoll: (date) {
          add(GetMyCurrentBookingEvent.get());
        },
        interval: const Duration(seconds: 20),
      );
    }

    on<_Get>(_onGetCurrentBooking);
    on<_Start>(_onStartPolling);
    on<_Resume>(_onResumePolling);
    on<_Stop>(_onStopPolling);
  }

  Future<void> _onGetCurrentBooking(
    _Get get,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    emit(GetMyCurrentBookingState.loading());
    final result = await _bookingRepository.getMyBookings();
    result.fold((failure) => emit(GetMyCurrentBookingState.failure(failure)), (
      booking,
    ) {
      if (booking.isEmpty && _booking != null) {
        emit(
          GetMyCurrentBookingState.loaded((
            isFinishedRide: false,
            booking: _booking!,
          )),
        );
        return;
      }
      _booking = booking.last;
      emit(
        GetMyCurrentBookingState.loaded((
          isFinishedRide: false,
          booking: booking.last,
        )),
      );
    });
  }

  Future<void> _onStartPolling(
    _Start start,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    await _pollingService.start(_pollingId);
  }

  Future<void> _onResumePolling(
    _Resume resume,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    await _pollingService.resume(_pollingId);
  }

  Future<void> _onStopPolling(
    _Stop stop,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    _pollingService.stop(_pollingId);
  }
}
