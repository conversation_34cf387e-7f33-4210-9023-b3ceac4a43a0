// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_to_cart_booking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AddToCartBookingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AddToCartBookingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddToCartBookingEvent()';
  }
}

/// @nodoc

class _Create implements AddToCartBookingEvent {
  const _Create({required this.passengerOfferedFare});

  final double passengerOfferedFare;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Create &&
            (identical(other.passengerOfferedFare, passengerOfferedFare) ||
                other.passengerOfferedFare == passengerOfferedFare));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerOfferedFare);

  @override
  String toString() {
    return 'AddToCartBookingEvent.create(passengerOfferedFare: $passengerOfferedFare)';
  }
}

/// @nodoc

class _ShareRidingMode implements AddToCartBookingEvent {
  const _ShareRidingMode({required this.isShareRidingMode});

  final bool isShareRidingMode;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShareRidingMode &&
            (identical(other.isShareRidingMode, isShareRidingMode) ||
                other.isShareRidingMode == isShareRidingMode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isShareRidingMode);

  @override
  String toString() {
    return 'AddToCartBookingEvent.shareRidingMode(isShareRidingMode: $isShareRidingMode)';
  }
}

/// @nodoc

class InititalizeRoutes implements AddToCartBookingEvent {
  const InititalizeRoutes({
    required this.direction,
    required final List<LatLngWithAddress> locations,
  }) : _locations = locations;

  final DirectionRouteModel direction;
  final List<LatLngWithAddress> _locations;
  List<LatLngWithAddress> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InititalizeRoutes &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    direction,
    const DeepCollectionEquality().hash(_locations),
  );

  @override
  String toString() {
    return 'AddToCartBookingEvent.inititalizeRoutes(direction: $direction, locations: $locations)';
  }
}

/// @nodoc

class _ScheduleTripDateTime implements AddToCartBookingEvent {
  const _ScheduleTripDateTime({required this.scheduleDataTime});

  final DateTime scheduleDataTime;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ScheduleTripDateTime &&
            (identical(other.scheduleDataTime, scheduleDataTime) ||
                other.scheduleDataTime == scheduleDataTime));
  }

  @override
  int get hashCode => Object.hash(runtimeType, scheduleDataTime);

  @override
  String toString() {
    return 'AddToCartBookingEvent.scheduleTripDateTime(scheduleDataTime: $scheduleDataTime)';
  }
}

/// @nodoc

class _InitialPassengerCount implements AddToCartBookingEvent {
  const _InitialPassengerCount({required this.passengerCount});

  final int passengerCount;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InitialPassengerCount &&
            (identical(other.passengerCount, passengerCount) ||
                other.passengerCount == passengerCount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerCount);

  @override
  String toString() {
    return 'AddToCartBookingEvent.initialPassengerCount(passengerCount: $passengerCount)';
  }
}
