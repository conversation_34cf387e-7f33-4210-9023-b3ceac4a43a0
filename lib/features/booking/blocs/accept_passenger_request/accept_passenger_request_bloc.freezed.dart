// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accept_passenger_request_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AcceptPassengerRequestEvent {
  int get passengerId;
  double get fareAmount;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AcceptPassengerRequestEvent &&
            (identical(other.passengerId, passengerId) ||
                other.passengerId == passengerId) &&
            (identical(other.fareAmount, fareAmount) ||
                other.fareAmount == fareAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerId, fareAmount);

  @override
  String toString() {
    return 'AcceptPassengerRequestEvent(passengerId: $passengerId, fareAmount: $fareAmount)';
  }
}

/// @nodoc

class _Accept implements AcceptPassengerRequestEvent {
  const _Accept({required this.passengerId, required this.fareAmount});

  @override
  final int passengerId;
  @override
  final double fareAmount;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Accept &&
            (identical(other.passengerId, passengerId) ||
                other.passengerId == passengerId) &&
            (identical(other.fareAmount, fareAmount) ||
                other.fareAmount == fareAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerId, fareAmount);

  @override
  String toString() {
    return 'AcceptPassengerRequestEvent.accept(passengerId: $passengerId, fareAmount: $fareAmount)';
  }
}
