// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cancel_request_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CancelRequestEvent {
  int get bookingId;
  String get reasonForCancel;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CancelRequestEvent &&
            (identical(other.bookingId, bookingId) ||
                other.bookingId == bookingId) &&
            (identical(other.reasonForCancel, reasonForCancel) ||
                other.reasonForCancel == reasonForCancel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bookingId, reasonForCancel);

  @override
  String toString() {
    return 'CancelRequestEvent(bookingId: $bookingId, reasonForCancel: $reasonForCancel)';
  }
}

/// @nodoc

class _CancelRequest implements CancelRequestEvent {
  const _CancelRequest(this.bookingId, this.reasonForCancel);

  @override
  final int bookingId;
  @override
  final String reasonForCancel;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CancelRequest &&
            (identical(other.bookingId, bookingId) ||
                other.bookingId == bookingId) &&
            (identical(other.reasonForCancel, reasonForCancel) ||
                other.reasonForCancel == reasonForCancel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bookingId, reasonForCancel);

  @override
  String toString() {
    return 'CancelRequestEvent.cancelRequest(bookingId: $bookingId, reasonForCancel: $reasonForCancel)';
  }
}
