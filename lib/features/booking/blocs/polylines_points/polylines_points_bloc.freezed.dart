// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'polylines_points_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PolylinesPointsEvent {
  List<BookingDetailModel> get locations;
  String get uniqueValue;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PolylinesPointsEvent &&
            const DeepCollectionEquality().equals(other.locations, locations) &&
            (identical(other.uniqueValue, uniqueValue) ||
                other.uniqueValue == uniqueValue));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(locations),
    uniqueValue,
  );

  @override
  String toString() {
    return 'PolylinesPointsEvent(locations: $locations, uniqueValue: $uniqueValue)';
  }
}

/// @nodoc

class GetPolylines implements PolylinesPointsEvent {
  const GetPolylines({
    required final List<BookingDetailModel> locations,
    required this.uniqueValue,
  }) : _locations = locations;

  final List<BookingDetailModel> _locations;
  @override
  List<BookingDetailModel> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  @override
  final String uniqueValue;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetPolylines &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ) &&
            (identical(other.uniqueValue, uniqueValue) ||
                other.uniqueValue == uniqueValue));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_locations),
    uniqueValue,
  );

  @override
  String toString() {
    return 'PolylinesPointsEvent.getPolylines(locations: $locations, uniqueValue: $uniqueValue)';
  }
}
