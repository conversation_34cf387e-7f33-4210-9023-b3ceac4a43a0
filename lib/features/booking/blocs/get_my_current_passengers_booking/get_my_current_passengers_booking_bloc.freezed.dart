// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_my_current_passengers_booking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetMyCurrentPassengersBookingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetMyCurrentPassengersBookingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent()';
  }
}

/// @nodoc

class _GetCurrentBooking implements GetMyCurrentPassengersBookingEvent {
  const _GetCurrentBooking();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetCurrentBooking);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.getCurrentBooking()';
  }
}

/// @nodoc

class _StopCurrentBookings implements GetMyCurrentPassengersBookingEvent {
  const _StopCurrentBookings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _StopCurrentBookings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.stopCurrentBookings()';
  }
}

/// @nodoc

class _ResumeCurrentBookings implements GetMyCurrentPassengersBookingEvent {
  const _ResumeCurrentBookings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ResumeCurrentBookings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.resumeCurrentBookign()';
  }
}
