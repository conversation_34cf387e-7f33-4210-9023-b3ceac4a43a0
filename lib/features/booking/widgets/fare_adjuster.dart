import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';

/// A widget that allows users to adjust a numerical fare value
/// using plus and minus buttons, with visual feedback and animations.
/// It also tracks a 'last confirmed' fare to prevent decreasing below it.
class FareAdjuster extends StatefulWidget {
  final int initialFare;

  final ValueChanged<int> onFareChanged;

  const FareAdjuster({
    super.key,
    required this.initialFare,
    required this.onFareChanged,
  });

  @override
  State<FareAdjuster> createState() => _FareAdjusterState();
}

class _FareAdjusterState extends State<FareAdjuster> {
  late int _currentFare; // Manages the current fare value within this widget
  late int _lastConfirmedFare; // Stores the fare after it's 'raised'
  int _selectedIndex =
      -1; // Tracks which button (if any) was last pressed for visual feedback

  @override
  void initState() {
    super.initState();
    _currentFare = widget.initialFare;
    _lastConfirmedFare =
        widget
            .initialFare; // Initially, the current fare is the last confirmed one
  }

  /// Sets the current fare as the new 'last confirmed' fare.
  /// This method is called by the parent widget when the 'Raise Fare' button is pressed.
  void confirmCurrentFare() {
    setState(() {
      _lastConfirmedFare = _currentFare;
      _selectedIndex = -1; // Reset button selection after confirmation
    });
  }

  /// Decreases the current fare by 5, ensuring it doesn't go below the last confirmed fare.
  void _decreaseFare() {
    // Only allow decreasing if the new fare would not be less than the last confirmed fare
    if (_currentFare - 5 >= _lastConfirmedFare) {
      setState(() {
        _selectedIndex = 0; // Mark the decrease button as selected
        _currentFare -= 5;
        widget.onFareChanged(_currentFare); // Notify parent of the change
      });
    } else {
      // Optional: Add a visual shake or a subtle hint that it cannot go lower
      // For now, we just disable the button visually.
    }
  }

  /// Increases the current fare by 5.
  void _increaseFare() {
    setState(() {
      _selectedIndex = 1; // Mark the increase button as selected
      _currentFare += 5;
      widget.onFareChanged(_currentFare); // Notify parent of the change
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Determine if the decrease button should be enabled
    final bool canDecrease = _currentFare > _lastConfirmedFare;

    return Column(
      children: [
        // "Your Offer" text for clarity
        Center(
          child: Text(
            "Your Offer",
            style: textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
        const SizedBox(height: AppStyles.space8),

        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Decrease Fare Button
            GestureDetector(
              // Only allow tap if canDecrease is true
              onTap: canDecrease ? _decreaseFare : null,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
                height: 55,
                width: 80,
                decoration: BoxDecoration(
                  borderRadius: AppStyles.radiusLg,
                  color:
                      canDecrease
                          ? (_selectedIndex == 0
                              ? Colors.green[400]
                              : Colors.grey[200])
                          : Colors.grey[350], // Disabled color
                  boxShadow:
                      canDecrease && _selectedIndex == 0
                          ? [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.4),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ]
                          : [],
                ),
                child: Center(
                  child: Text(
                    '-5',
                    style: textTheme.headlineSmall?.copyWith(
                      color:
                          canDecrease
                              ? (_selectedIndex == 0
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Colors.black87)
                              : Colors.grey[500], // Disabled text color
                    ),
                  ),
                ),
              ),
            ),
            Row(
              children: [
                Text(
                  'NPR',
                  style: textTheme.displayMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple[700],
                  ),
                ),
                SizedBox(width: 10),
                // Animated Fare Display
                AnimatedSwitcher(
                  duration: const Duration(
                    milliseconds: 350,
                  ), // Slightly longer for smoother slide
                  transitionBuilder: (
                    Widget child,
                    Animation<double> animation,
                  ) {
                    // Determine slide direction based on fare change
                    final offsetAnimation = Tween<Offset>(
                      begin: const Offset(
                        0.0,
                        0.5,
                      ), // Start from slightly below
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic, // Modern cubic easing
                      ),
                    );

                    return SlideTransition(
                      position: offsetAnimation,
                      child: FadeTransition(
                        // Combine with fade for elegance
                        opacity: animation,
                        child: child,
                      ),
                    );
                  },
                  child: Text(
                    "$_currentFare",
                    key: ValueKey<int>(
                      _currentFare,
                    ), // Key is crucial for AnimatedSwitcher
                    style: textTheme.displayMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color:
                          Colors
                              .deepPurple[700], // Deeper purple for stronger presence
                    ),
                  ),
                ),
              ],
            ),

            // Increase Fare Button
            GestureDetector(
              onTap: _increaseFare,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
                height: 55,
                width: 80,
                decoration: BoxDecoration(
                  borderRadius: AppStyles.radiusLg,
                  color:
                      _selectedIndex == 1
                          ? Colors.green[400]
                          : Colors.grey[200],
                  boxShadow:
                      _selectedIndex == 1
                          ? [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.4),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ]
                          : [],
                ),
                child: Center(
                  child: Text(
                    '+5',
                    style: textTheme.headlineSmall?.copyWith(
                      color:
                          _selectedIndex == 1
                              ? Theme.of(context).colorScheme.onPrimary
                              : Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
