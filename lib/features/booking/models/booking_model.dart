import 'booking_details.dart';

class BookingModel {
  final int bookingId;
  final String bookingDate;
  final String bookingStartDate;
  final bool isSharedBookingMode;
  final int totalDistanceInMeter;
  final int passengerCount;
  final double totalFareAmount;
  final double acceptedFareAmount;
  final String passengerName;
  final String riderName;
  final String bookingCancelDate;
  final String cancelUserName;
  final String reasonForCancel;
  final String paymentStatus;
  final String serviceStatus;
  final List<BookingDetailModel> bookingDetailViews;

  BookingModel({
    required this.bookingId,
    required this.bookingDate,
    required this.bookingStartDate,
    required this.isSharedBookingMode,
    required this.totalDistanceInMeter,
    required this.passengerCount,
    required this.totalFareAmount,
    required this.acceptedFareAmount,
    required this.passengerName,
    required this.riderName,
    required this.bookingCancelDate,
    required this.cancelUserName,
    required this.reasonForCancel,
    required this.paymentStatus,
    required this.serviceStatus,
    required this.bookingDetailViews,
  });

  factory BookingModel.fromMap(Map<String, dynamic> map) {
    return BookingModel(
      bookingId: map['BookingId'] ?? 0,
      bookingDate: map['BookingDate'] ?? '',
      bookingStartDate: map['BookingStartDate'] ?? '',
      isSharedBookingMode: map['IsSharedBookingMode'] ?? false,
      totalDistanceInMeter: map['TotalDistanceInMeter'] ?? 0,
      passengerCount: map['PassengerCount'] ?? 0,
      totalFareAmount: (map['TotalFareAmount'] ?? 0).toDouble(),
      acceptedFareAmount: (map['AcceptedFareAmount'] ?? 0).toDouble(),
      passengerName: map['PassengerName'] ?? '',
      riderName: map['RiderName'] ?? '',
      bookingCancelDate: map['BookingCancelDate'] ?? '',
      cancelUserName: map['CancelUserName'] ?? '',
      reasonForCancel: map['ReasonForCancel'] ?? '',
      paymentStatus: map['PaymentStatus'] ?? '',
      serviceStatus: map['ServiceStatus'] ?? '',
      bookingDetailViews:
          (map['BookingDetailViews'] as List<dynamic>?)
              ?.map((e) => BookingDetailModel.fromMap(e))
              .toList() ??
          [],
    );
  }
}
