// ignore_for_file: public_member_api_docs, sort_constructors_first
// booking_cart_request.dart
import 'package:safari_yatri/features/booking/models/booking_details.dart';



class BookingAddToCartModel {
  final String? bookingStartDate;
  final String? bookingStartTime;
  final bool isSharedBookingMode;
  final double passengerFareAmount;
  final double systemFareAmount;
  final int passengerCount;
  final int totalDistanceInMeter;
  final List<BookingDetailModel> cartDetailViews;

  BookingAddToCartModel({
    this.totalDistanceInMeter = 0,
    this.bookingStartDate,
    this.bookingStartTime,
    this.isSharedBookingMode = false,
    this.passengerFareAmount = 0.0,
    required this.systemFareAmount,
    this.passengerCount = 1,
    this.cartDetailViews = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      "TotalDistanceInMeter": totalDistanceInMeter,
      'BookingStartDate': bookingStartDate,
      'BookingStartTime': bookingStartTime,
      'IsSharedBookingMode': isSharedBookingMode,
      'PassengerFareAmount': passengerFareAmount,
      'CartDetailViews': cartDetailViews.map((e) => e.toMap()).toList(),
      'PassengerCount': passengerCount,
      'TotalFareAmount': systemFareAmount,
    };
  }

  factory BookingAddToCartModel.fromMap(Map<String, dynamic> map) {
    return BookingAddToCartModel(
      totalDistanceInMeter: map['TotalDistanceInMeter'] ?? 0,
      bookingStartDate: map['BookingStartDate'],
      bookingStartTime: map['BookingStartTime'],
      isSharedBookingMode: map['IsSharedBookingMode'] ?? false,
      passengerFareAmount: (map['PassengerFareAmount'] ?? 0).toDouble(),
      systemFareAmount: (map['TotalFareAmount'] ?? 0).toDouble(),
      passengerCount: map['PassengerCount'] ?? 1,
      cartDetailViews:
          (map['CartDetailViews'] as List<dynamic>? ?? [])
              .map((e) => BookingDetailModel.fromMap(e))
              .toList(),
    );
  }

  BookingAddToCartModel copyWith({
    String? bookingStartDate,
    String? bookingStartTime,
    bool? isSharedBookingMode,
    double? passengerOfferedFare,
    double? systemFareAmount,
    int? passengerCount,
    List<BookingDetailModel>? cartDetailViews,
  }) {
    return BookingAddToCartModel(
      passengerCount: passengerCount ?? this.passengerCount,
      systemFareAmount: systemFareAmount ?? this.systemFareAmount,
      totalDistanceInMeter: totalDistanceInMeter,
      bookingStartDate: bookingStartDate ?? this.bookingStartDate,
      bookingStartTime: bookingStartTime ?? this.bookingStartTime,
      isSharedBookingMode: isSharedBookingMode ?? this.isSharedBookingMode,
      passengerFareAmount: passengerOfferedFare ?? passengerFareAmount,
      cartDetailViews: cartDetailViews ?? this.cartDetailViews,
    );
  }
}
