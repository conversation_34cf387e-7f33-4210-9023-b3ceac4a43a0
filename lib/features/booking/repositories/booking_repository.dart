import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import '../../../core/network/api_service.dart';
import '../models/accepting_riders_model.dart';
import '../models/booking_add_to_cart.dart';
import '../models/new_passenger_view_model.dart';

abstract interface class BookingRepository {
  FutureEither<String> bookingAddToCart({
    required BookingAddToCartModel bookingAddToCart,
  });

  ///admin will get taht
  FutureEither<List<BookingModel>> getAllBookings({
    String bookingStatus = "CurrentOnly",
  });

  FutureEither<BookingModel> getBookingDetail({required int id});

  FutureEither<List<BookingModel>> getMyBookings({
    String bookingStatus = "CurrentOnly",
  });

  FutureEither<List<BookingModel>> getMyPassengerBookings({
    String bookingStatus = "CurrentOnly",
  });

  FutureEither<List<GetMyAcceptingRidersModel>> getMyAcceptingRiders();

  FutureEither<List<NewPassengerModel>> getNewPassengers();

  FutureEither<String> acceptBooking({
    required int passengerId,
    required double fareAmount,
  });

  FutureEither<String> insertBooking({required int riderId});

  FutureEither<String> cancelBooking({
    required int bookingId,
    required String reasonForCancel,
  });

  FutureEither<String> clearMyCart();
  FutureEither<String> serviceStart(int bookingId);
  FutureEither<String> serviceComplete(int bookingId);
}

class BookingRepositoryI implements BookingRepository {
  final ApiService _apiService;
  BookingRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  //##--------------POST: ADD TO CART----------------##
  @override
  FutureEither<String> bookingAddToCart({
    required BookingAddToCartModel bookingAddToCart,
  }) async {
    final booking = bookingAddToCart.toMap();
    print(jsonEncode(booking).toString());
    final response = await _apiService.post<String>(
      'Booking/AddToCart',
      data: booking,
    );

    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  //##--------------GET: GET LIST /{includeCancelled}----------------##
  @override
  FutureEither<List<BookingModel>> getAllBookings({
    String bookingStatus = "CurrentOnly",
  }) async {
    final response = await _apiService.get<List<dynamic>>(
      'Booking/GetList/$bookingStatus',
    );

    return response.fold((failure) => Left(failure), (data) {
      final bookings =
          data
              .map((e) => BookingModel.fromMap(e as Map<String, dynamic>))
              .toList();
      return Right(bookings);
    });
  }

  //##--------------GET: GET ITEM /{id}----------------##
  @override
  FutureEither<BookingModel> getBookingDetail({required int id}) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      'Booking/GetItem/$id',
    );

    return response.fold((failure) => Left(failure), (data) {
      final booking = BookingModel.fromMap(data);
      return Right(booking);
    });
  }

  //##--------------POST: ACCEPT BOOKING BY RIDER----------------##
  @override
  FutureEither<String> acceptBooking({
    required int passengerId,
    required double fareAmount,
  }) async {
    final response = await _apiService.post<String>(
      'Booking/AcceptBookingByRider?passengerId=$passengerId&fareAmount=$fareAmount',
    );

    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  //##--------------POST: INSERT BOOKING----------------##
  @override
  FutureEither<String> insertBooking({required int riderId}) async {
    final response = await _apiService.post<String>(
      'Booking/Insert?riderId=$riderId',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  //##--------------PUT: CANCEL BOOKING /{bookingId}/{reasonForCancel}----------------##
  @override
  FutureEither<String> cancelBooking({
    required int bookingId,
    required String reasonForCancel,
  }) async {
    final encodedReason = Uri.encodeComponent(reasonForCancel);
    final response = await _apiService.put<String>(
      'Booking/Cancel/$bookingId/$encodedReason',
    );

    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  //##--------------GET: MY BOOKINGS /{includeCancelled}----------------##
  @override
  FutureEither<List<BookingModel>> getMyBookings({
    String bookingStatus = "CurrentOnly",
  }) async {
    final response = await _apiService.get<List<dynamic>>(
      'Booking/MyBookings/$bookingStatus',
    );

    return response.fold((failure) => Left(failure), (data) {
      final bookings =
          data
              .map((e) => BookingModel.fromMap(e as Map<String, dynamic>))
              .toList();
      return Right(bookings);
    });
  }

  //##--------------GET: MY PASSENGER BOOKINGS /{includeCancelled}----------------##
  @override
  FutureEither<List<BookingModel>> getMyPassengerBookings({
    String bookingStatus = "CurrentOnly",
  }) async {
    final response = await _apiService.get<List<dynamic>>(
      'Booking/MyPassengerBookings/$bookingStatus',
    );

    return response.fold((failure) => Left(failure), (data) {
      final bookings =
          data
              .map((e) => BookingModel.fromMap(e as Map<String, dynamic>))
              .toList();
      return Right(bookings);
    });
  }

  //##--------------GET: GET MY ACCEPTING RIDERS----------------##
  @override
  FutureEither<List<GetMyAcceptingRidersModel>> getMyAcceptingRiders() async {
    final response = await _apiService.get<List<dynamic>>(
      'Booking/GetMyAcceptingRiders',
    );

    return response.fold((failure) => Left(failure), (data) {
      final riders =
          data
              .map(
                (e) => GetMyAcceptingRidersModel.fromMap(
                  e as Map<String, dynamic>,
                ),
              )
              .toList();
      return Right(riders);
    });
  }

  //##--------------GET: GET NEW PASSENGERS----------------##
  @override
  FutureEither<List<NewPassengerModel>> getNewPassengers() async {
    final response = await _apiService.get<List<dynamic>>(
      'Booking/GetNewPassengers',
    );

    return response.fold((failure) => Left(failure), (data) {
      final passengers =
          data
              .map((e) => NewPassengerModel.fromMap(e as Map<String, dynamic>))
              .toList();
      return Right(passengers);
    });
  }

  //##--------------DELETE: CLEAR MY CART----------------##
  @override
  FutureEither<String> clearMyCart() async {
    final response = await _apiService.delete<String>('Booking/ClearMyCart');
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  @override
  FutureEither<String> serviceComplete(int bookingId) async {
    final response = await _apiService.put<String>(
      'Booking/ServiceComplete/$bookingId',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  @override
  FutureEither<String> serviceStart(int bookingId) async {
    final response = await _apiService.put<String>(
      'Booking/ServiceStart/$bookingId',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }
}
