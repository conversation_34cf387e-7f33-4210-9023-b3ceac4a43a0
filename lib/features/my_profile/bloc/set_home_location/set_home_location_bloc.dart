import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';

import '../../repositories/my_profile_repository.dart';

part 'set_home_location_event.dart';
part 'set_home_location_state.dart';
part 'set_home_location_bloc.freezed.dart';

class SetHomeLocationBloc
    extends Bloc<SetHomeLocationEvent, SetHomeLocationState> {
  final MyProfileRepository _myProfileRepository;
  SetHomeLocationBloc({required MyProfileRepository repo})
    : _myProfileRepository = repo,
      super(SetHomeLocationState.initial()) {
    // on<SetHomeLocationEvent>(_onSetHomeLocation);

    // Future<void> _onSetHomeLocation(
    //   SetHomeLocationEvent event,
    //   Emitter<SetHomeLocationState> emit,
    // ) async {
    //   emit(SetHomeLocationState.loading());

    //   final result = await _myProfileRepository.setHomeLocation(
    //     lat:event. 2.34444,
    //     lng: 3.5555,
    //   );

    //   result.fold(
    //     (failure) => emit(SetHomeLocationState.failure(failure)),
    //     (data) => emit(SetHomeLocationState.loaded(data)),
    //   );
    // }
  }
}
