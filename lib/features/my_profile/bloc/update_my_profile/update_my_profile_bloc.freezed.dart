// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_my_profile_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UpdateMyProfileEvent {
  UpdateUserDataModel get updateUserData;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateMyProfileEvent &&
            (identical(other.updateUserData, updateUserData) ||
                other.updateUserData == updateUserData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, updateUserData);

  @override
  String toString() {
    return 'UpdateMyProfileEvent(updateUserData: $updateUserData)';
  }
}

/// @nodoc

class _UpdateMyProfile implements UpdateMyProfileEvent {
  const _UpdateMyProfile(this.updateUserData);

  @override
  final UpdateUserDataModel updateUserData;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateMyProfile &&
            (identical(other.updateUserData, updateUserData) ||
                other.updateUserData == updateUserData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, updateUserData);

  @override
  String toString() {
    return 'UpdateMyProfileEvent.updateMyProfile(updateUserData: $updateUserData)';
  }
}
