import 'dart:convert';


class GetUserProfileDetailsModel {
  final String loginId;
  final String loginStatus;
  final String userType;
  final String registeredDate;
  final String registeredTime;
  final String otpSentDate;
  final String otpSentTime;
  final String activationDate;
  final String activationTime;
  final double walletBalance;
  final String? profilePicture;
  final String? address;
  final int userId;
  final String userName;
  final String phoneNo;
  final String gender;
  final String emailAddress;
  final double homeLatitude;
  final double homeLongitude;
  final double currentLatitude;
  final double currentLongitude;

  GetUserProfileDetailsModel({
    this.address,
    required this.loginId,
    required this.loginStatus,
    required this.userType,
    required this.registeredDate,
    required this.registeredTime,
    required this.otpSentDate,
    required this.otpSentTime,
    required this.activationDate,
    required this.activationTime,
    required this.walletBalance,
    this.profilePicture,
    required this.userId,
    required this.userName,
    required this.phoneNo,
    required this.gender,
    required this.emailAddress,
    required this.homeLatitude,
    required this.homeLongitude,
    required this.currentLatitude,
    required this.currentLongitude,
  });

  Map<String, dynamic> toMap() {
    return {
      'UserAddress': address,
      'LoginId': loginId,
      'LoginStatus': loginStatus,
      'UserType': userType,
      'RegisteredDate': registeredDate,
      'RegisteredTime': registeredTime,
      'OtpSentDate': otpSentDate,
      'OtpSentTime': otpSentTime,
      'ActivationDate': activationDate,
      'ActivationTime': activationTime,
      'WalletBalance': walletBalance,
      'ProfilePicture': profilePicture,
      'UserId': userId,
      'UserName': userName,
      'PhoneNo': phoneNo,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'HomeLatitude': homeLatitude,
      'HomeLongitude': homeLongitude,
      'CurrentLatitude': currentLatitude,
      'CurrentLongitude': currentLongitude,
    };
  }

  factory GetUserProfileDetailsModel.fromMap(Map<String, dynamic> map) {
    return GetUserProfileDetailsModel(
      address: map['UserAddress'],
      loginId: map['LoginId'] as String,
      loginStatus: map['LoginStatus'] as String,
      userType: map['UserType'] as String,
      registeredDate: map['RegisteredDate'] as String,
      registeredTime: map['RegisteredTime'] as String,
      otpSentDate: map['OtpSentDate'] as String,
      otpSentTime: map['OtpSentTime'] as String,
      activationDate: map['ActivationDate'] as String,
      activationTime: map['ActivationTime'] as String,
      walletBalance: (map['WalletBalance'] as num).toDouble(),
      profilePicture: map['ProfilePicture'] as String?,
      userId: map['UserId'] as int,
      userName: map['UserName'] as String,
      phoneNo: map['PhoneNo'] as String,
      gender: map['Gender'] as String,
      emailAddress: map['EmailAddress'] as String,
      homeLatitude: (map['HomeLatitude'] as num).toDouble(),
      homeLongitude: (map['HomeLongitude'] as num).toDouble(),
      currentLatitude: (map['CurrentLatitude'] as num).toDouble(),
      currentLongitude: (map['CurrentLongitude'] as num).toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory GetUserProfileDetailsModel.fromJson(String source) =>
      GetUserProfileDetailsModel.fromMap(
        json.decode(source) as Map<String, dynamic>,
      );

      
}
