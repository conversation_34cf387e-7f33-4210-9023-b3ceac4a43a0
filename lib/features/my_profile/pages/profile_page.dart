import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/update_my_profile/update_my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/upload_profile_image/upload_profile_image_bloc.dart';
import 'package:safari_yatri/features/my_profile/model/update_user_data_model.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/widget/custom_appbar.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isEditing = false;
  XFile? profileImage;

  TextEditingController userNameController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController genderController = TextEditingController();
  TextEditingController userAddressController = TextEditingController();

  @override
  void dispose() {
    userNameController.dispose();
    phoneNumberController.dispose();
    emailAddressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text('My Profile'),
        actions: [
          Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: Colors.blueAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  isEditing = true;
                });
              },
              icon: Icon(
                LucideIcons.pencil,
                color: Colors.blueAccent,
                size: 16,
              ),
            ),
          ),
        ],
      ),
      body: BlocBuilder<MyProfileBloc, MyProfileState>(
        builder: (context, state) {
          return state.when(
            initial: () => SizedBox(),
            loading: () => CircularProgressIndicator(),
            loaded: (data) {
              userNameController.text = data.userName;
              phoneNumberController.text = data.phoneNo;
              emailAddressController.text = data.emailAddress;
              userAddressController.text = data.address ?? 'No Address';
              genderController.text = data.gender;
              return RefreshIndicator(
                onRefresh: () async {
                  if (profileImage != null) {
                    sl<MyProfileBloc>().add(MyProfileEvent.get(true));
                  }
                },
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppStyles.space12,
                  ),
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Column(
                    spacing: AppStyles.space12,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: AppStyles.space32),
                      Center(
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            CircleAvatar(
                              radius: 48,
                              backgroundColor: Colors.grey[200],
                              child: ClipOval(
                                child: SizedBox(
                                  width: 96,
                                  height: 96,
                                  child:
                                      profileImage != null
                                          ? ImageUtility.displayImageFromXFile(
                                            profileImage,
                                          )
                                          : ImageUtility.displayImageFromBase64(
                                            data.profilePicture,
                                          ),
                                ),
                              ),
                            ),
                            Visibility(
                              visible: isEditing,
                              child: Positioned(
                                bottom: -1,
                                right: -1,
                                child: GestureDetector(
                                  onTap: () async {
                                    profileImage =
                                        await ImageUtility.showImageSourceDialog(
                                          context,
                                        );
                                    setState(() {});
                                  },
                                  child: Container(
                                    height: 28,
                                    width: 28,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.grey.shade300,
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.edit,
                                      size: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppStyles.space24),
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          "Personal Info",
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      CustomFormFieldWidget(
                        label: 'Full Name',
                        prefixIcon: LucideIcons.user,
                        controller: userNameController,
                        enabled: isEditing,
                      ),
                      CustomFormFieldWidget(
                        label: 'Phone Number',
                        prefixIcon: LucideIcons.phone,
                        controller: phoneNumberController,
                        enabled: false,
                      ),
                      CustomFormFieldWidget(
                        label: 'Email Address',
                        prefixIcon: LucideIcons.mail,
                        controller: emailAddressController,
                        enabled: isEditing,
                      ),
                      CustomFormFieldWidget(
                        label: 'Current Address',
                        prefixIcon: LucideIcons.mapPin,
                        controller: userAddressController,
                        enabled: isEditing,
                      ),
                      CustomFormFieldWidget(
                        label: 'Gender',
                        prefixIcon: LucideIcons.venus,
                        controller: genderController,
                        enabled: isEditing,
                      ),

                      Container(
                        padding: EdgeInsets.symmetric(
                          vertical: 16,
                          horizontal: 24,
                        ),
                        decoration: BoxDecoration(
                          color: Color(0xF9FBFDFF),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildLocationButton(
                              icon: LucideIcons.house,
                              label: 'Home',
                              subLabel: 'Set Address',
                              onTap:
                                  isEditing
                                      ? () {
                                        context.pushNamed(
                                          AppRoutesName.setHomeLocation,
                                        );
                                      }
                                      : null,
                            ),
                            Container(
                              width: 1,
                              height: 48,
                              color: Colors.grey[300],
                              margin: EdgeInsets.symmetric(horizontal: 16),
                            ),
                            _buildLocationButton(
                              icon: LucideIcons.briefcase,
                              label: 'Work',
                              subLabel: 'Set Address',
                              onTap:
                                  isEditing
                                      ? () {
                                        CustomToast.showInfo(
                                          'This features is currently not Available',
                                        );
                                      }
                                      : null,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppStyles.space24),

                      // RemoteUserRoleWidget(riderChild: DocumentInfo()),
                    ],
                  ),
                ),
              );
            },
            failure: (failure) {
              return Text(failure.message);
            },
          );
        },
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.fromLTRB(
          AppStyles.space12,
          0,
          AppStyles.space12,
          AppStyles.space16,
        ),
        child: BlocListener<UploadProfileImageBloc, UploadProfileImageState>(
          listener: (context, state) {
            state.whenOrNull(
              loading: () => AppLoadingDialog.show(context),
              failure: (failure) {
                AppLoadingDialog.hide(context);
                CustomToast.showError(failure.message);
                return;
              },
              loaded: (data) {
                CustomToast.showSuccess(data);
                AppLoadingDialog.hide(context);
                setState(() {
                  isEditing = false;
                });
                context.pop();
              },
            );
          },
          child: BlocListener<UpdateMyProfileBloc, UpdateMyProfileState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () => AppLoadingDialog.show(context),
                failure: (failure) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showError(failure.message);
                  return;
                },
                loaded: (data) {
                  CustomToast.showSuccess(data);
                  AppLoadingDialog.hide(context);
                  setState(() {
                    isEditing = false;
                  });
                  context.pop();
                },
              );
            },
            child: CustomButtonPrimary(
              title: 'Confirm',
              onPressed:
                  isEditing
                      ? () {
                        UpdateUserDataModel userDataModel = UpdateUserDataModel(
                          userName: userNameController.text,
                          userAddress: userAddressController.text,
                          emailAddress: emailAddressController.text,
                          gender: genderController.text,
                        );
                        sl<UpdateMyProfileBloc>().add(
                          UpdateMyProfileEvent.updateMyProfile(userDataModel),
                        );
                        if (profileImage != null) {
                          sl<UploadProfileImageBloc>().add(
                            UploadProfileImageEvent.uploadProfileImage(
                              profileImage!,
                            ),
                          );
                        }
                      }
                      : null,
            ),
          ),
        ),
      ),
    );
  }
}

Widget _buildLocationButton({
  required IconData icon,
  required String label,
  required String subLabel,
  required void Function()? onTap,
}) {
  final bool isEnabled = onTap != null;

  return Opacity(
    opacity: isEnabled ? 1.0 : 0.5,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Colors.grey[100],
            radius: 20,
            child: Icon(icon, size: 20, color: Colors.black54),
          ),
          SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                subLabel,
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
