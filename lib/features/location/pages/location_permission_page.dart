import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';

import '../../../core/widget/custom_alertbox.dart';

class LocationPermissionPage extends StatefulWidget {
  const LocationPermissionPage({super.key});

  @override
  State<LocationPermissionPage> createState() => _LocationPermissionPageState();
}

class _LocationPermissionPageState extends State<LocationPermissionPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late final AnimationController _lottieController;
  bool _isDialogOpened = false;
  @override
  void initState() {
    super.initState();
    // sl<LocationPermissionBloc>().add(
    //   LocationPermissionEvent.checkPermissionStatus(),
    // );
    WidgetsBinding.instance.addObserver(this);
    _lottieController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      sl<LocationPermissionBloc>().add(
        LocationPermissionEvent.checkPermissionStatus(),
      );
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _lottieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocConsumer<LocationPermissionBloc, LocationPermissionState>(
          listener: (context, state) {
            state.whenOrNull(
              loaded: (grantedStatus) {
                context.goNamed(AppRoutesName.roleBaseNavigatorPage);
              },
              failure: (failure) {
                if (_isDialogOpened) {
                  _isDialogOpened = false;
                  context.pop();
                }
                return _showErrorDialog(failure);
              },
            );
          },
          builder:
              (context, state) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: AppStyles.space64),
                    Lottie.asset(
                      ImageConstant.location,
                      controller: _lottieController,
                      onLoaded: (composition) {
                        _lottieController
                          ..duration = composition.duration
                          ..forward();
                      },
                      height: 300,
                      fit: BoxFit.contain,
                    ),

                    Text(
                      "Enable Location Permission",
                      textAlign: TextAlign.center,
                      style: TextTheme.of(context).headlineSmall,
                    ),
                    Text(
                      "To get great service you need to provide location permission. You can always change permission from settings",
                      textAlign: TextAlign.center,
                    ),

                    Spacer(),

                    CustomButtonPrimary(
                      title: "Give Permissions",
                      onPressed: () {
                        sl<LocationPermissionBloc>().add(
                          const LocationPermissionEvent.checkAndRequestPermission(),
                        );
                      },
                    ),

                    Gap(20),
                  ],
                ),
              ),
        ),
      ),
    );
  }

  Future<void> _showErrorDialog(Failure failure) async {
    setState(() {
      _isDialogOpened = true;
    });
    // String title = "Location Permission Required";
    String message =
        "We require your precise location to seamlessly connect you to nearby services providers.\n\nPlease turn on device location.";
    // String description = failure.message;
    String assetName = ImageConstant.appLogo;
    List<Widget> actions = [];

    if (failure is LocationPermissionDeniedFailure ||
        failure is LocationPermissionLimitedFailure ||
        failure is LocationPermissionProvisionalFailure ||
        failure is LocationFailureWithMessage) {
      actions.add(
        CustomButtonPrimary(
          onPressed: () {
            // Navigator.pop(context);
            sl<LocationPermissionBloc>().add(
              const LocationPermissionEvent.checkAndRequestPermission(),
            );
          },
          title: "Give Location Permisson",
        ),
      );
    } else if (failure is LocationPermissionPermanentlyDeniedFailure ||
        failure is LocationPermissionRestrictedFailure) {
      actions.add(
        CustomTextButton(
          title: 'Open Settings',
          onPressed: () {
            sl<LocationPermissionBloc>().add(
              const LocationPermissionEvent.openAppSettings(),
            );
          },
        ),
      );
    }
    showCustomDialog(
      context: context,
      assetName: assetName,
      message: message,
      actions: actions,
      canPop: true,
    );
  }
}
