import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/features/passenger/location/widgets/passenger_map.dart';

import '../../../core/theme/app_styles.dart';
import '../../../core/widget/custom_search_field_cupertino.dart';

class SetLocationPages extends StatefulWidget {
  const SetLocationPages({super.key});

  @override
  State<SetLocationPages> createState() => _SetLocationPagesState();
}

class _SetLocationPagesState extends State<SetLocationPages> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PassengerMap(),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppStyles.space12,
              vertical: AppStyles.space32,
            ),
            child: CustomBackButton(),
          ),

          _buildBottomBar(),
        ],
      ),
    );
  }
}

Widget _buildBottomBar() {
  return Positioned(
    bottom: 0,
    left: 0,
    right: 0,
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(AppStyles.space12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            spacing: AppStyles.space12,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomSearchBarWidget(
                leadingIcon: Icons.home,
                placeholder: 'Search address....',
                // controller: ,
                onChanged: (text) {
                  log("Searching: $text");
                },
              ),

              Divider(color: Colors.grey[300]),
              CustomButtonPrimary(
                title: 'Confrim Home Address',
                onPressed: () {},
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
