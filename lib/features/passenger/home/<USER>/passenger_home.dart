import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/common/widgets/location_permission_widget.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/bottom_sheet.dart';
import 'package:safari_yatri/core/widget/current_location_navigator_button.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_drawer_btn.dart';
import 'package:safari_yatri/core/widget/custom_shimmer.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location/location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/home/<USER>/passenger_count_widget.dart';
import 'package:safari_yatri/features/passenger/location/models/location_picking_type.dart';
import 'package:safari_yatri/features/passenger/location/widgets/passenger_map.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../../../core/utils/localization_utils.dart';
import '../../../../core/services/show_case_services.dart';
import '../../../../core/widget/show_case_wrapper.dart';
import '../../../drawer/pages/passenger_drawer_page.dart';
import '../../fare_offer/pages/fare_offer_page.dart';

class PassengerHomePage extends StatefulWidget {
  const PassengerHomePage({super.key});

  @override
  State<PassengerHomePage> createState() => _PassengerHomePageState();
}

class _PassengerHomePageState extends State<PassengerHomePage> {
  PassengerRouteLoaded? _passengerRouteLoaded;
  bool _isPassengerSelectedDestination = false;
  bool _showOnceFailure = false;

  final GlobalKey _countKey = GlobalKey();
  final GlobalKey _userProfileKey = GlobalKey();
  final GlobalKey _pickupKey = GlobalKey();
  final GlobalKey _destinationKey = GlobalKey();
  final GlobalKey _findKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    sl<MyProfileBloc>().add(MyProfileEvent.get());
    sl<LocationPermissionBloc>().add(
      const LocationPermissionEvent.checkPermissionStatus(),
    );
    sl<PassengerRouteBloc>().add(const PassengerRouteEvent.initRoutes());
    sl<LocalUserModeCubit>().getUserMode();
    sl<LocationBloc>().add(const LocationEvent.startTracking());
    sl<GetRideShiftBloc>().add(GetRideShiftEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    return LocationPermissionListenerWidget(
      child: CustomShowcaseWrapper(
        child: Scaffold(
          drawer: buildPassengerAppDrawer(context),
          body: BlocConsumer<PassengerRouteBloc, PassengerRouteState>(
            listener: (context, state) {
              state.whenOrNull(
                loaded: (data) {
                  _isPassengerSelectedDestination =
                      data.dropoffLocations.isNotEmpty;
                  _showOnceFailure = true;

                  // Show intro of features
                  ShowcaseService(
                    context: context,
                    showcaseKeys: [
                      _userProfileKey,
                      _countKey,
                      _pickupKey,
                      _destinationKey,
                      _findKey,
                    ],
                  ).startIfFirstTime();
                },
                failure: (failure) {
                  if (_showOnceFailure) {
                    CustomToast.showError(failure.message);
                  }
                },
              );
            },
            builder: (context, state) {
              return _build(state, bottomInset);
            },
          ),
        ),
      ),
    );
  }

  Widget _build(PassengerRouteState state, double bottomInset) {
    return SafeArea(
      child: SizedBox(
        height: double.maxFinite,
        width: double.maxFinite,
        child: Stack(
          children: [
            Positioned.fill(child: PassengerMap()),
            Showcase(
              key: _userProfileKey,
              description: L.t.showCaseDrawerDescription,
              child: _pickUpLocationVisibility(
                child: buildDrawerButton(context),
              ),
            ),
            _pickUpLocationVisibility(child: _buildBottomBar()),
          ],
        ),
      ),
    );
  }

  Widget _pickUpLocationVisibility({required Widget child}) {
    return BlocBuilder<MapMovementCubit, MapMovementState>(
      builder: (context, state) {
        if (state == MapMovementState.moving) {
          return Visibility(visible: false, child: child);
        } else {
          return child;
        }
      },
    );
  }

  Positioned _buildBottomBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Column(
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.only(right: 12, bottom: 8),
              child:
                  _isPassengerSelectedDestination
                      ? const SizedBox()
                      : const CurrentLocationNavigatorButton(),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppStyles.space16,
              vertical: AppStyles.space20,
            ).copyWith(bottom: AppStyles.space16),
            decoration: BoxDecoration(
              color: T.c.surface,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppStyles.space16),
              ),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: BlocBuilder<PassengerRouteBloc, PassengerRouteState>(
              builder: (context, state) {
                state.whenOrNull(
                  loaded: (data) {
                    _passengerRouteLoaded = data;
                  },
                );
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _passengerRouteLoaded == null
                        ? SizedBox(
                          height: 12,
                          width: 20,
                          child: AppCustomShimmer(),
                        )
                        : const SizedBox(),
                    _passengerRouteLoaded == null
                        ? _passengerCountLoadingWidget()
                        : Showcase(
                          key: _countKey,
                          description: L.t.showCasePassengerCoutDescription,
                          child: PassengerCountWidget(
                            onPassengerCountChanged: (selectedPassengerCount) {
                              sl<AddToCartBookingBloc>().add(
                                AddToCartBookingEvent.initialPassengerCount(
                                  passengerCount: selectedPassengerCount,
                                ),
                              );
                            },
                          ),
                        ),
                    const Gap(16),
                    Showcase(
                      key: _pickupKey,
                      description: L.t.showCasePickUpLocationDescription,
                      child: _buildCustomTextField(
                        hasValue: _passengerRouteLoaded != null,
                        onTap: _onPickUpLocation,
                        isPickUp: true,
                        value:
                            _passengerRouteLoaded?.pickupLocation.address ??
                            'From',
                      ),
                    ),
                    const Gap(8),
                    Showcase(
                      key: _destinationKey,
                      description: L.t.showCaseDestinationLocationDescription,
                      child: _buildCustomTextField(
                        hasValue: _passengerRouteLoaded != null,
                        onTap: _onDestination,
                        value: _getDestinationLabel(),
                        isPickUp: false,
                        suffixIcon:
                            _passengerRouteLoaded != null &&
                                    _passengerRouteLoaded!
                                        .dropoffLocations
                                        .isNotEmpty &&
                                    _passengerRouteLoaded!
                                            .dropoffLocations
                                            .length <=
                                        4
                                ? IconButton(
                                  onPressed:
                                      () => context.pushNamed(
                                        AppRoutesName.enterRoute,
                                        extra: {
                                          'type': LocationType.destination,
                                          'shouldHidePickupLocation': true,
                                        },
                                      ),
                                  icon: Icon(Icons.add, color: T.c.onSurface),
                                )
                                : null,
                      ),
                    ),
                    const Gap(16),
                    _passengerRouteLoaded == null
                        ? const AppCustomShimmer()
                        : Showcase(
                          key: _findKey,
                          description: L.t.showCaseFindRiderDescription,
                          child: CustomButtonPrimary(
                            title: L.t.buttonTitle,
                            onPressed: _onFindDrivers,
                          ),
                        ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Row _passengerCountLoadingWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children:
          List.generate(
              4,
              (index) => const SizedBox(width: 60, child: AppCustomShimmer()),
            ).expand((widget) sync* {
              yield widget;
              yield const SizedBox(width: 12);
            }).toList()
            ..removeLast(),
    );
  }

  void _onDestination() {
    if ((_passengerRouteLoaded?.dropoffLocations.length ?? 0) > 1) {
      showModalBottomSheet(
        context: context,
        builder: (context) => _buildDestinationsBottomSheet(),
      );
    } else {
      context.pushNamed(
        AppRoutesName.enterRoute,
        extra: {'type': LocationType.destination},
      );
    }
  }

  void _onFindDrivers() {
    if (_passengerRouteLoaded?.dropoffLocations == null ||
        _passengerRouteLoaded?.dropoffLocations.isEmpty == true) {
      _onDestination();
      return;
    }
    if (_passengerRouteLoaded?.directionRoute == null) {
      CustomToast.showError(L.t.passengerHomeDirectionErrorText);
      return;
    }

    sl<AddToCartBookingBloc>().add(
      AddToCartBookingEvent.inititalizeRoutes(
        direction: _passengerRouteLoaded!.directionRoute!,
        locations: [
          _passengerRouteLoaded!.pickupLocation,
          ..._passengerRouteLoaded!.dropoffLocations.map((e) => e),
        ],
      ),
    );

    appShowModalBottomSheet(context, child: const FareOfferPage());
  }

  void _onPickUpLocation() {
    context.pushNamed(
      AppRoutesName.enterRoute,
      extra: {'type': LocationType.pickup},
    );
  }

  String _getDestinationLabel() {
    final dropoffs = _passengerRouteLoaded?.dropoffLocations;
    if (dropoffs == null || dropoffs.isEmpty) {
      return '';
    } else if (dropoffs.length == 1) {
      return dropoffs.first.address;
    } else {
      return '${dropoffs.length} stops';
    }
  }

  Widget _buildDestinationsBottomSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: T.c.onSurface.withOpacity(0.2),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const Gap(16),
          Text(
            L.t.passengerHomeBottomSheetDestination,
            style: T.t.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._passengerRouteLoaded!.dropoffLocations.asMap().entries.map((
            entry,
          ) {
            int idx = entry.key;
            var location = entry.value;
            return ListTile(
              leading: const Icon(LucideIcons.mapPin200),
              title: Text(location.address),
              trailing: Icon(Icons.cancel, color: T.c.error),
              onTap: () {
                sl<PassengerRouteBloc>().add(
                  PassengerRouteEvent.removeDestinationLocation(index: idx),
                );
                context.pop();
              },
            );
          }),
          const Gap(8),
        ],
      ),
    );
  }

  Widget _buildCustomTextField({
    required String value,
    required bool isPickUp,
    Widget? suffixIcon,
    required VoidCallback onTap,
    bool hasValue = true,
  }) {
    final TextEditingController controller = TextEditingController(text: value);

    if (!hasValue) {
      return const AppCustomShimmer();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      child: TextField(
        readOnly: true,
        controller: controller,
        onTap: onTap,
        maxLines: 1,
        style: TextStyle(color: T.c.onSurfaceVariant),
        scrollPhysics: const NeverScrollableScrollPhysics(),
        decoration: InputDecoration(
          hintText: isPickUp ? "From" : "To",
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: T.c.surfaceContainerHighest,
          suffixIcon: suffixIcon,
          prefixIcon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder:
                (child, anim) => ScaleTransition(scale: anim, child: child),
            child:
                controller.text.isNotEmpty
                    ? Icon(
                      Icons.radio_button_checked,
                      key: const ValueKey('focusedIcon'),
                      color:
                          isPickUp
                              ? AppColors.pickUpLocationColor
                              : AppColors.destinationLocationColor,
                    )
                    : const Icon(Icons.search, key: ValueKey('defaultIcon')),
          ),
        ),
      ),
    );
  }
}
