import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class PassengerCountWidget extends StatefulWidget {
  const PassengerCountWidget({
    super.key,
    required this.onPassengerCountChanged,
  });

  final Function(int numberOfPassengers) onPassengerCountChanged;

  @override
  State<PassengerCountWidget> createState() => _PassengerCountWidgetState();
}

class _PassengerCountWidgetState extends State<PassengerCountWidget> {
  int _selectedPassengerBookingCount = 1;
  final List<int> availablePassengerCounts = [1, 2, 3, 4];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddToCartBookingBloc, AddToCartBookingState>(
      builder: (context, state) {
        state.whenOrNull(
          loaded: (data) {
            _selectedPassengerBookingCount = data.data?.passengerCount ?? 1;
          },
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Text('Number of Passengers'),
            // SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children:
                  availablePassengerCounts.map((count) {
                    final isSelected = _selectedPassengerBookingCount == count;

                    return Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedPassengerBookingCount = count;
                          });
                          widget.onPassengerCountChanged(count);
                        },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 250),
                          curve: Curves.easeInOut,
                          height: 52,
                          width: 52,
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? Colors.green.shade400
                                    : Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? Colors.green.shade500
                                      : Colors.grey.shade300,
                              width: isSelected ? 2 : 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    isSelected
                                        ? Colors.green.withOpacity(0.35)
                                        : Colors.black.withOpacity(0.05),
                                blurRadius: isSelected ? 10 : 4,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Center(
                            child: _buildPassengerIconLayout(count, isSelected),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPassengerIconLayout(int count, bool isSelected) {
    return Wrap(
      spacing: 2,
      runSpacing: 2,
      alignment: WrapAlignment.center,
      children: List.generate(
        count,
        (index) => Icon(
          LucideIcons.userRound,
          size: 16,
          color: isSelected ? Colors.white : Colors.black87,
        ),
      ),
    );
  }
}
