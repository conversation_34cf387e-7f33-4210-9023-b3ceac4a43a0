import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class PassengerCountWidget extends StatefulWidget {
  const PassengerCountWidget({
    super.key,
    required this.onPassengerCountChanged,
  });

  final Function(int numberOfPassengers) onPassengerCountChanged;

  @override
  State<PassengerCountWidget> createState() => _PassengerCountWidgetState();
}

class _PassengerCountWidgetState extends State<PassengerCountWidget> {
  int _selectedPassengerBookingCount = 1;
  final List<int> availablePassengerCounts = [1, 2, 3, 4];

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return BlocBuilder<AddToCartBookingBloc, AddToCartBookingState>(
      builder: (context, state) {
        state.whenOrNull(
          loaded: (data) {
            _selectedPassengerBookingCount = data.data?.passengerCount ?? 1;
          },
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title with better styling
            Text(
              'Number of Passengers',
              style: T
                  .t(context)
                  .titleMedium
                  ?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: T.c(context).onSurface,
                  ),
            ),
            const SizedBox(height: 16),

            // Passenger count selection with improved design
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color:
                    isDark
                        ? T.c(context).surface.withAlpha(128)
                        : T.c(context).surfaceContainerHighest.withAlpha(77),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      isDark
                          ? T.c(context).outline.withAlpha(51)
                          : T.c(context).outline.withAlpha(26),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children:
                    availablePassengerCounts.map((count) {
                      final isSelected =
                          _selectedPassengerBookingCount == count;

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 2.0),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedPassengerBookingCount = count;
                            });
                            widget.onPassengerCountChanged(count);
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOutCubic,
                            height: 56,
                            width: 56,
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  isSelected
                                      ? T.c(context).primary
                                      : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  isSelected
                                      ? null
                                      : Border.all(
                                        color: Colors.transparent,
                                        width: 1,
                                      ),
                              boxShadow:
                                  isSelected
                                      ? [
                                        BoxShadow(
                                          color:
                                              isDark
                                                  ? T
                                                      .c(context)
                                                      .primary
                                                      .withAlpha(77)
                                                  : T
                                                      .c(context)
                                                      .primary
                                                      .withAlpha(51),
                                          blurRadius: 12,
                                          offset: const Offset(0, 4),
                                          spreadRadius: 0,
                                        ),
                                        BoxShadow(
                                          color:
                                              isDark
                                                  ? T
                                                      .c(context)
                                                      .primary
                                                      .withAlpha(26)
                                                  : T
                                                      .c(context)
                                                      .primary
                                                      .withAlpha(13),
                                          blurRadius: 24,
                                          offset: const Offset(0, 8),
                                          spreadRadius: 0,
                                        ),
                                      ]
                                      : null,
                            ),
                            child: Center(
                              child: _buildPassengerIconLayout(
                                count,
                                isSelected,
                                isDark,
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),

            // Subtle helper text
            const SizedBox(height: 12),
            Text(
              'Select the number of passengers for your trip',
              style: T
                  .t(context)
                  .bodySmall
                  ?.copyWith(
                    color:
                        isDark
                            ? T.c(context).onSurfaceVariant.withAlpha(179)
                            : T.c(context).onSurfaceVariant.withAlpha(153),
                  ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPassengerIconLayout(int count, bool isSelected, bool isDark) {
    // Create a more sophisticated icon layout
    Widget buildIcon(int index) {
      return Container(
        padding: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? T.c(context).onPrimary.withAlpha(26)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          LucideIcons.userRound,
          size: count > 2 ? 12 : 14,
          color:
              isSelected
                  ? T.c(context).onPrimary
                  : isDark
                  ? T.c(context).onSurfaceVariant.withAlpha(204)
                  : T.c(context).onSurfaceVariant.withAlpha(179),
        ),
      );
    }

    // Layout based on passenger count for better visual hierarchy
    switch (count) {
      case 1:
        return buildIcon(0);
      case 2:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [buildIcon(0), const SizedBox(width: 2), buildIcon(1)],
        );
      case 3:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            buildIcon(0),
            const SizedBox(height: 2),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [buildIcon(1), const SizedBox(width: 2), buildIcon(2)],
            ),
          ],
        );
      case 4:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [buildIcon(0), const SizedBox(width: 2), buildIcon(1)],
            ),
            const SizedBox(height: 2),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [buildIcon(2), const SizedBox(width: 2), buildIcon(3)],
            ),
          ],
        );
      default:
        return buildIcon(0);
    }
  }
}
