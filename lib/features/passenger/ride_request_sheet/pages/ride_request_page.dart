// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/widget/animated_text_loader.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/features/booking/blocs/clear_my_cart/clear_my_cart_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_accepting_riders/get_my_accepting_riders_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';
import 'package:safari_yatri/features/booking/widgets/driver_accepted_cart_offer.dart';
import 'package:safari_yatri/features/booking/widgets/fare_adjuster.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_styles.dart';

class RideRequestPage extends StatefulWidget {
  const RideRequestPage({super.key, required this.bookingAddToCartModel});
  final BookingAddToCartModel bookingAddToCartModel;

  @override
  State<RideRequestPage> createState() => _RideRequestPageState();
}

class _RideRequestPageState extends State<RideRequestPage> {
  int _currentFare = 0;
  late String pickUpSourceAddress;
  late String destinationAddress;
  late final GetMyAcceptingRidersBloc _getMyAcceptingRiderBloc;
  LatLng _currentLatLng = const LatLng(27.7172, 85.3240);
  late CurrentLocationBloc _currentLocationBloc;
  GoogleMapController? _mapController;
  @override
  void initState() {
    super.initState();

    _currentFare = widget.bookingAddToCartModel.passengerFareAmount.toInt();
    pickUpSourceAddress =
        widget.bookingAddToCartModel.cartDetailViews.first.sourceAddress;
    destinationAddress =
        widget.bookingAddToCartModel.cartDetailViews.first.destinationAddress;
    _getMyAcceptingRiderBloc = sl<GetMyAcceptingRidersBloc>();
    // _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.get());
    _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.resume());

    _currentLocationBloc = sl<CurrentLocationBloc>();
    _currentLocationBloc.add(const CurrentLocationEvent.getCurrentLocation());
  }

  @override
  void dispose() {
    super.dispose();
    _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.stop());
  }

  void _onFareChanged(int newFare) {
    setState(() {
      _currentFare = newFare;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(child: _buildStack());
  }

  Widget _buildStack() {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Stack(
      children: [
        AbsorbPointer(
          child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
            bloc: _currentLocationBloc,
            listener: (context, state) {
              state.maybeWhen(
                loaded: (data) {
                  _currentLatLng = LatLng(data.latitude, data.longitude);
                  _mapController?.animateCamera(
                    CameraUpdate.newCameraPosition(
                      CameraPosition(
                        target: _currentLatLng,
                        zoom: kMapInitialZoom,
                      ),
                    ),
                  );
                },
                orElse: () {},
              );
            },
            child: CustomGoogleMap(
              onMapCreated: (controller) {
                _mapController = controller;
              },
              initialCameraPosition: CameraPosition(
                target: _currentLatLng,
                zoom: kMapInitialZoom,
              ),
            ),
          ),
        ),
        BlocBuilder<GetMyAcceptingRidersBloc, GetMyAcceptingRidersState>(
          bloc: _getMyAcceptingRiderBloc,
          builder: (context, state) {
            return state.maybeWhen(
              orElse: () => SizedBox(),
              loaded: (riders) {
                if (riders.isEmpty) return SizedBox();
                return DriverAcceptedCartOffer(drivers: riders);
              },
            );
          },
        ),
        DraggableScrollableSheet(
          initialChildSize: 0.43,
          minChildSize: 0.43,
          maxChildSize: 0.65,
          builder:
              (context, scrollController) => Material(
                child: Container(
                  padding: const EdgeInsets.all(AppStyles.space16),
                  decoration: BoxDecoration(
                    color:
                        AppColors.lightSurface, // Consistent light background
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 15,
                        spreadRadius: 3,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: AnimatedTextLoader(
                            switchDuration: Duration(seconds: 5),
                            messages: [
                              "Searching for the best nearby driver...",
                              "Hang tight! We're finding your ride.",
                              "Almost there... getting a driver for you.",
                              "Your comfort ride is just a tap away 🚗💨",
                              "Making sure the driver gets your location 📍",
                            ],
                          ),
                        ),

                        Divider(color: Colors.grey[300], height: 1),
                        const SizedBox(height: AppStyles.space24),
                        FareAdjuster(
                          initialFare: _currentFare,
                          onFareChanged: _onFareChanged,
                        ),
                        const SizedBox(height: AppStyles.space32),
                        CustomButtonPrimary(
                          title: 'Raise Fare',
                          textColor: AppColors.darkSurface,
                          backgroundColor: Colors.green[500],
                          onPressed: () {
                            CustomToast.showSuccess(
                              'You raised the fare to NPR $_currentFare',
                            );
                          },
                        ),
                        const SizedBox(height: AppStyles.space24),
                        _buildSectionHeader(context, 'Payment', textTheme),
                        _buildPaymentDetail(context, textTheme, _currentFare),
                        const SizedBox(height: AppStyles.space16),
                        _buildSectionHeader(
                          context,
                          'Your Current Ride',
                          textTheme,
                        ),
                        _buildRideDetail(
                          context,
                          Icons.radio_button_checked,
                          AppColors.pickUpLocationColor,
                          pickUpSourceAddress,
                          textTheme,
                        ),
                        _buildRideDetail(
                          context,
                          Icons.location_on,
                          AppColors.destinationLocationColor,
                          destinationAddress,
                          textTheme,
                        ),
                        const SizedBox(height: AppStyles.space32),
                        BlocListener<ClearMyCartBloc, ClearMyCartState>(
                          child: CustomButtonPrimary(
                            title: 'Cancel Request',
                            textColor: AppColors.lightError,
                            backgroundColor: Colors.grey[200],
                            onPressed: () {
                              sl<ClearMyCartBloc>().add(
                                ClearMyCartEvent.clearMyCart(),
                              );
                            },
                          ),
                          listener: (context, state) {
                            state.whenOrNull(
                              failure: (failure) {
                                AppLoadingDialog.hide(context);
                                CustomToast.showError(failure.message);
                              },
                              loading: () {
                                AppLoadingDialog.show(context);
                              },
                              loaded: (data) {
                                context.pop();
                                AppLoadingDialog.hide(context);
                                CustomToast.showSuccess(
                                  "Request Cancel successful!!",
                                );
                              },
                            );
                          },
                        ),
                        const SizedBox(height: AppStyles.space16),
                      ],
                    ),
                  ),
                ),
              ),
        ),
      ],
    );
  }

  /// Helper method to build section headers with consistent styling.
  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    TextTheme textTheme,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(bottom: AppStyles.space8),
        child: Text(
          title,
          style: textTheme.titleSmall?.copyWith(
            color: Colors.grey[700],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Helper method to build payment detail row.
  Widget _buildPaymentDetail(
    BuildContext context,
    TextTheme textTheme,
    int fare,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          const Icon(Icons.currency_bitcoin, color: Colors.amber, size: 20),
          const SizedBox(width: AppStyles.space8),
          Text(
            "NPR $fare",
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color:
                  AppColors.darkSurface, // Use a darker, more prominent color
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to build individual ride detail rows (pickup/destination).
  Widget _buildRideDetail(
    BuildContext context,
    IconData icon,
    Color color,
    String text,
    TextTheme textTheme,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppStyles.space4),
        child: Row(
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: AppStyles.space8),
            Text(
              text,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600, // Semi-bold for good readability
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
