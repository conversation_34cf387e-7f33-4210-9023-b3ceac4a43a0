import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';
import 'package:safari_yatri/features/booking/widgets/share_booking_mode.dart';

import '../widgets/time_picking_widget.dart';

class FareOfferPage extends StatefulWidget {
  const FareOfferPage({super.key});

  @override
  State<FareOfferPage> createState() => _FareOfferPageState();
}

class _FareOfferPageState extends State<FareOfferPage> {
  final double _initialPrefixPadding = 120;
  double prefixIconPadding = 120;
  DateTime? _selectedTime; // Replaces _selectedDuration

  final TextEditingController _fareController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  ///automatically listener call vairako xa so jaba button pressed vako hunxa tab chaii listner garney
  bool _isPressedDoneButton = false;
  @override
  initState() {
    super.initState();
    sl<GetVehicleTypeBloc>().add(const GetVehicleTypeEvent.get());
  }

  @override
  void dispose() {
    _fareController.dispose();
    super.dispose();
  }

  Future<void> _showMaterialTimePicker() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder:
          (_) => ScheduleTripBottomSheet(
            onScheduleConfirmed: (DateTime pickedDateTime) {
              setState(() {
                _selectedTime = pickedDateTime;
              });

              sl<AddToCartBookingBloc>().add(
                AddToCartBookingEvent.scheduleTripDateTime(
                  scheduleDataTime: _selectedTime!,
                ),
              );
              Navigator.of(context).pop();
            },
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Padding(
      padding: EdgeInsets.only(bottom: bottomInset),
      child: Padding(
        padding: const EdgeInsets.all(AppStyles.space12),

        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,

            children: [
              Row(
                children: [
                  const Expanded(child: SizedBox()),
                  Expanded(
                    flex: 2,
                    child: Text(
                      L.t.fareOfferScreenTitle,
                      style: Theme.of(context).textTheme.headlineMedium,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.grey.shade100,
                          ),
                          child: const Center(
                            child: Icon(Icons.close, size: 18),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Gap(AppStyles.space12),

              BlocBuilder<AddToCartBookingBloc, AddToCartBookingState>(
                builder: (context, state) {
                  BookingAddToCartModel? bookingCart = state.whenOrNull(
                    loaded: (data) => data.data,
                  );
                  if (bookingCart != null) {
                    return Column(
                      children: [
                        Row(
                          children: [
                            Text(
                           L.t.fareOfferScreenSuggestedFare,
                              style: TextTheme.of(context).titleMedium,
                            ),
                            Gap(AppStyles.space8),
                            Text(
                              bookingCart.systemFareAmount.toString(),
                              style: TextTheme.of(context).titleMedium
                                  ?.copyWith(color: AppColors.brandGreen),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Text(
                             L.t.fareOfferScreenDistance,
                              style: TextTheme.of(context).titleMedium,
                            ),
                            Gap(AppStyles.space8),
                            Text(
                              "${bookingCart.totalDistanceInMeter}m",
                              style: TextTheme.of(context).titleMedium
                                  ?.copyWith(color: AppColors.brandGreen),
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                  return const SizedBox();
                },
              ),

              const SizedBox(height: AppStyles.space12),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppStyles.space48,
                ),
                child: _buildFareTextField(),
              ),
              const SizedBox(height: AppStyles.space16),

              ShareBookingModeWidget(),
              const SizedBox(height: AppStyles.space16),

              scheduleTimerPicker(),

              const SizedBox(height: AppStyles.space20),

              BlocListener<AddToCartBookingBloc, AddToCartBookingState>(
                listener: (context, state) {
                  if (!_isPressedDoneButton) return;
                  state.whenOrNull(
                    loading: () => AppLoadingDialog.show(context),
                    loaded: (data) {
                      AppLoadingDialog.hide(context);
                      Navigator.of(context).pop(); //poping bottomsheet
                      context.pushNamed(
                        AppRoutesName.rideRequestPage,
                        extra: data.data?.toMap(),
                      );
                    },
                    failure: (failure) {
                      _isPressedDoneButton = false;
                      CustomToast.showError(failure.message);

                      AppLoadingDialog.hide(context);
                    },
                  );
                },
                child: CustomButtonPrimary(
                  title: L.t.fareOfferScreenDone,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      final offeredFair =
                          double.tryParse(_fareController.text.trim()) ?? 0.0;
                      _isPressedDoneButton = true;

                      sl<AddToCartBookingBloc>().add(
                        AddToCartBookingEvent.create(
                          passengerOfferedFare: offeredFair,
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  GestureDetector scheduleTimerPicker() {
    return GestureDetector(
      onTap: _showMaterialTimePicker,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _selectedTime == null
                  ? L.t.fareOfferScreenScheduleTime
                  : DateFormat('EEE, MMM d • hh:mm a').format(_selectedTime!),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),

            const Icon(Icons.access_time, size: 20, color: Colors.teal),
          ],
        ),
      ),
    );
  }

  Widget _buildFareTextField() {
    return BlocBuilder<GetVehicleTypeBloc, GetVehicleTypeState>(
      builder: (context, state) {
        GetVehicleType? vehicle = state.whenOrNull(
          loaded: (data) => data.first,
        );
        double negotitationBottomValue =
            (vehicle?.negotiationBottomValue ?? 20.0);
        return TextFormField(
          controller: _fareController,
          onChanged: (value) {
            setState(() {
              prefixIconPadding = _initialPrefixPadding - (value.length * 12.5);
              if (value.isEmpty) {
                prefixIconPadding = _initialPrefixPadding;
              }

              if (prefixIconPadding < 0) {
                prefixIconPadding = 0;
              }
            });
          },
          keyboardType: TextInputType.number,
          textAlign: TextAlign.start,
          style: const TextStyle(fontSize: 40, fontWeight: FontWeight.bold),
          maxLength: 6,
          decoration: InputDecoration(
            counterText: '',
            prefixIconConstraints: const BoxConstraints(),
            prefixIcon: Padding(
              padding: EdgeInsetsDirectional.only(start: prefixIconPadding),
              child: const Padding(
                padding: EdgeInsets.only(right: 10),
                child: Text(
                  'रु',
                  style: TextStyle(
                    fontSize: 40,
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            border: const UnderlineInputBorder(),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),

            errorStyle: const TextStyle(color: Colors.redAccent, fontSize: 14),
          ),

          validator: (value) {
            if (value == null || value.isEmpty) {
              return L.t.fareOfferScreenEnterFareError;
            }
            final double? fare = double.tryParse(value);
            if (fare == null) {
              return L.t.fareOfferScreenInvalidFareError;
            }
            if (fare < 0) {
              return L.t.fareOfferScreenNegativeFareError;
            }
            if (fare < negotitationBottomValue) {
              return L.t.fareOfferScreenMinimumFareError(negotitationBottomValue);
            }
            return null;
          },
        );
      },
    );
  }
}
