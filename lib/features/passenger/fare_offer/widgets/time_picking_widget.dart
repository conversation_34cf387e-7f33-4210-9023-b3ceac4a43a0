import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';

class ScheduleTripBottomSheet extends StatefulWidget {
  final Function(DateTime) onScheduleConfirmed;

  const ScheduleTripBottomSheet({super.key, required this.onScheduleConfirmed});

  @override
  State<ScheduleTripBottomSheet> createState() =>
      _ScheduleTripBottomSheetState();
}

class _ScheduleTripBottomSheetState extends State<ScheduleTripBottomSheet> {
  DateTime? selectedDate;
  TimeOfDay? selectedTime;

  void _showCustomDatePicker() {
    DateTime now = DateTime.now();
    DateTime initial = DateTime(now.year, now.month, now.day);
    DateTime tempPicked = selectedDate ?? initial;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return SizedBox(
          height: 300,
          child: Column(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  "Select Pickup Date",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: selectedDate ?? initial,
                  minimumDate: initial,
                  maximumDate: initial.add(const Duration(days: 7)),
                  onDateTimeChanged: (val) => tempPicked = val,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomButtonPrimary(
                  title: 'Confirm',
                  onPressed: () {
                    setState(() => selectedDate = tempPicked);
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showCustomTimePicker() {
    DateTime now = DateTime.now();
    DateTime tempPicked = DateTime(
      now.year,
      now.month,
      now.day,
      selectedTime?.hour ?? now.hour,
      selectedTime?.minute ?? now.minute,
    );

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return SizedBox(
          height: 300,
          child: Column(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  "Select Pickup Time",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.time,
                  initialDateTime: tempPicked,
                  use24hFormat: false,
                  onDateTimeChanged: (val) => tempPicked = val,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomButtonPrimary(
                  title: 'Confirm',
                  onPressed: () {
                    setState(
                      () => selectedTime = TimeOfDay.fromDateTime(tempPicked),
                    );
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String get formattedDate {
    if (selectedDate == null) return "Pickup Date";
    return DateFormat('EEE, MMM d').format(selectedDate!);
  }

  String get formattedTime {
    if (selectedTime == null) return "Pickup Time";
    final now = DateTime.now();
    final dt = DateTime(
      now.year,
      now.month,
      now.day,
      selectedTime!.hour,
      selectedTime!.minute,
    );
    return DateFormat.jm().format(dt);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 4,
            width: 40,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const Text(
            "Schedule your trip",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: _showCustomDatePicker,
                  child: AbsorbPointer(
                    child: TextField(
                      readOnly: true,
                      decoration: InputDecoration(
                        hintText: formattedDate,
                        prefixIcon: const Icon(Icons.calendar_today_outlined),
                        filled: true,
                        fillColor: Colors.grey[100],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: _showCustomTimePicker,
                  child: AbsorbPointer(
                    child: TextField(
                      readOnly: true,
                      decoration: InputDecoration(
                        hintText: formattedTime,
                        prefixIcon: const Icon(Icons.access_time),
                        filled: true,
                        fillColor: Colors.grey[100],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          CustomButtonPrimary(
            title: 'Continue',
            onPressed:
                selectedDate != null && selectedTime != null
                    ? () {
                      final fullDateTime = DateTime(
                        selectedDate!.year,
                        selectedDate!.month,
                        selectedDate!.day,
                        selectedTime!.hour,
                        selectedTime!.minute,
                      );
                      widget.onScheduleConfirmed(fullDateTime);
                    }
                    : null,
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }
}
