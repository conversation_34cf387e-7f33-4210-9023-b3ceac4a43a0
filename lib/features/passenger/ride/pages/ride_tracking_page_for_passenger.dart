import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_booking/get_my_current_booking_bloc.dart';

class RideTrackingPageForPassenger extends StatefulWidget {
  const RideTrackingPageForPassenger({super.key});
  @override
  State<RideTrackingPageForPassenger> createState() =>
      _RideTrackingPageForPassengerState();
}

class _RideTrackingPageForPassengerState
    extends State<RideTrackingPageForPassenger> {
  GoogleMapController? _mapController;
  LatLng _currentLocation = const LatLng(27.7172, 85.3240);
  late CurrentLocationBloc _currentLocationBloc;
  late GetMyCurrentBookingBloc _myCurrentBookingBloc;
  late CancelRequestBloc _cancelRequestBloc;

  @override
  void initState() {
    super.initState();
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());
    _myCurrentBookingBloc =
        sl<GetMyCurrentBookingBloc>()
          ..add(GetMyCurrentBookingEvent.get())
          ..add(GetMyCurrentBookingEvent.start());
    _cancelRequestBloc = sl<CancelRequestBloc>();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  /// Shows a confirmation dialog before popping the screen.
  void _onPopInvoked(bool didPop, result) {
    if (didPop) return;

    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Action Not Allowed'),
          content: const Text(
            'You cannot exit this page right now.\n\nPlease wait until the ride is completed.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return CustomPopScope(
      onPopInvokedWithResult: _onPopInvoked,
      child: Scaffold(
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              _currentLocationBloc.add(
                CurrentLocationEvent.getCurrentLocation(),
              );
              _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.get());
            },
            child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
              bloc: _currentLocationBloc,
              listener: (context, state) {
                state.whenOrNull(
                  loaded: (data) {
                    _currentLocation = LatLng(data.latitude, data.longitude);
                    _mapController?.animateCamera(
                      CameraUpdate.newCameraPosition(
                        CameraPosition(
                          target: _currentLocation,
                          zoom: kMapInitialZoom,
                        ),
                      ),
                    );
                  },
                );
              },
              child: Column(
                children: [
                  Expanded(
                    child: CustomGoogleMap(
                      initialCameraPosition: CameraPosition(
                        target: _currentLocation,
                        zoom: kMapInitialZoom,
                      ),
                      myLocationEnabled: true,
                      onMapCreated: (controller) {
                        _mapController = controller;
                      },
                    ),
                  ),
                  BlocConsumer<
                    GetMyCurrentBookingBloc,
                    GetMyCurrentBookingState
                  >(
                    bloc: _myCurrentBookingBloc,
                    listener: (context, state) {},
                    builder: (context, state) {
                      return state.maybeWhen(
                        loading: () => _buildShimmerUI(),
                        failure:
                            (failure) => Padding(
                              padding: const EdgeInsets.all(24.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    failure.message,
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed:
                                        () => _myCurrentBookingBloc.add(
                                          GetMyCurrentBookingEvent.get(),
                                        ),
                                    child: const Text("Retry"),
                                  ),
                                ],
                              ),
                            ),
                        loaded: (data) {
                          return Card(
                            margin: const EdgeInsets.all(12),
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Ride Details",
                                    style: textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  _buildInfoRow(
                                    Icons.person_outline,
                                    "Rider",
                                    data.booking.riderName,
                                  ),
                                  const SizedBox(height: 12),
                                  _buildInfoRow(
                                    Icons.payments_outlined,
                                    "Fare",
                                    CurrencyFormatter.format(
                                      data.booking.acceptedFareAmount,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  _buildInfoRow(
                                    Icons.trip_origin_outlined,
                                    "Status",
                                    data.booking.serviceStatus,
                                  ),
                                  const SizedBox(height: 20),
                                  BlocListener<
                                    CancelRequestBloc,
                                    CancelRequestState
                                  >(
                                    bloc: _cancelRequestBloc,
                                    listener: _cancelRequestListener,
                                    child: SizedBox(
                                      width: double.infinity,
                                      child: FilledButton.tonal(
                                        onPressed:
                                            () => _onCancelPressed(
                                              data.booking.bookingId,
                                            ),
                                        style: FilledButton.styleFrom(
                                          backgroundColor: Colors.red
                                              .withOpacity(0.1),
                                          foregroundColor: Colors.red.shade800,
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 12,
                                          ),
                                        ),
                                        child: const Text("Cancel Ride"),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                        orElse: () => const SizedBox(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _cancelRequestListener(BuildContext context, CancelRequestState state) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (data) {
        AppLoadingDialog.hide(context);
        CustomToast.showSuccess("Ride cancelled successfully");
        sl<PassengerRouteBloc>().add(PassengerRouteEvent.resetPassengerRoute());
        context.goNamed(AppRoutesName.passengerHome);
      },
      failure: (failure) {
        AppLoadingDialog.hide(context);
        CustomToast.showError(failure.message);
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor, size: 20),
        const SizedBox(width: 12),
        Text("$label: ", style: Theme.of(context).textTheme.bodyMedium),
        Expanded(
          child: Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerUI() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Card(
        margin: const EdgeInsets.all(12),
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(width: 120, height: 24, color: Colors.white),
              const SizedBox(height: 16),
              _buildShimmerRow(),
              const SizedBox(height: 12),
              _buildShimmerRow(),
              const SizedBox(height: 12),
              _buildShimmerRow(),
              const SizedBox(height: 20),
              Container(
                width: double.infinity,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerRow() {
    return Row(
      children: [
        Container(width: 20, height: 20, color: Colors.white),
        const SizedBox(width: 12),
        Container(width: 50, height: 16, color: Colors.white),
        const Spacer(),
        Container(width: 100, height: 16, color: Colors.white),
      ],
    );
  }

  void _onCancelPressed(int bookingId) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 24,
          ),
          child: _CancelRideSheet(
            onReasonSelected: (reason) {
              // Close bottom sheet before showing dialog
              Navigator.pop(context);
              _cancelRequestBloc.add(
                CancelRequestEvent.cancelRequest(bookingId, reason),
              );
            },
          ),
        );
      },
    );
  }
}

class _CancelRideSheet extends StatefulWidget {
  final Function(String reason) onReasonSelected;
  const _CancelRideSheet({required this.onReasonSelected});
  @override
  State<_CancelRideSheet> createState() => _CancelRideSheetState();
}

class _CancelRideSheetState extends State<_CancelRideSheet> {
  final List<String> _reasons = [
    "Driver is late",
    "Changed my mind",
    "Booked by mistake",
    "Found another ride",
    "Driver not responding",
  ];
  String? _selectedReason;
  final TextEditingController _customReasonController = TextEditingController();

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Text(
            "Please select a reason for cancellation",
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 16),
        ..._reasons.map((reason) {
          return RadioListTile<String>(
            title: Text(reason),
            value: reason,
            groupValue: _selectedReason,
            onChanged: (val) {
              setState(() => _selectedReason = val);
              FocusScope.of(context).unfocus(); // Hide keyboard
              _customReasonController.clear();
            },
            contentPadding: EdgeInsets.zero,
          );
        }),
        const SizedBox(height: 8),
        TextField(
          controller: _customReasonController,
          decoration: const InputDecoration(
            labelText: "Other reason",
            hintText: "Enter your reason here",
            border: OutlineInputBorder(),
          ),
          onTap: () => setState(() => _selectedReason = null),
        ),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: () {
              final reason =
                  _customReasonController.text.trim().isNotEmpty
                      ? _customReasonController.text.trim()
                      : _selectedReason;

              if (reason == null || reason.isEmpty) {
                CustomToast.showError("Please select or enter a reason");
                return;
              }
              widget.onReasonSelected(reason);
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text("Confirm Cancellation"),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
