// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'passenger_route_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PassengerRouteEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PassengerRouteEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent()';
  }
}

/// @nodoc

class _PickUpLocation implements PassengerRouteEvent {
  const _PickUpLocation({required this.position});

  final LatLng position;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PickUpLocation &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  @override
  String toString() {
    return 'PassengerRouteEvent.pickUpLocation(position: $position)';
  }
}

/// @nodoc

class _DestinationLocation implements PassengerRouteEvent {
  const _DestinationLocation({required this.position});

  final LatLng position;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DestinationLocation &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  @override
  String toString() {
    return 'PassengerRouteEvent.destinationLocation(position: $position)';
  }
}

/// @nodoc

class _UpdateDestinationLocation implements PassengerRouteEvent {
  const _UpdateDestinationLocation({
    required this.position,
    required this.index,
  });

  final LatLng position;
  final int index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateDestinationLocation &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.updateDestinationLocation(position: $position, index: $index)';
  }
}

/// @nodoc

class _GetRoutes implements PassengerRouteEvent {
  const _GetRoutes();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetRoutes);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.getRoutes()';
  }
}

/// @nodoc

class _InitRoutes implements PassengerRouteEvent {
  const _InitRoutes();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InitRoutes);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.initRoutes()';
  }
}

/// @nodoc

class _ResetPassengerRoute implements PassengerRouteEvent {
  const _ResetPassengerRoute();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ResetPassengerRoute);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.resetPassengerRoute()';
  }
}

/// @nodoc

class _RemoveDestionationLocation implements PassengerRouteEvent {
  const _RemoveDestionationLocation({required this.index});

  final int index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveDestionationLocation &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.removeDestinationLocation(index: $index)';
  }
}

/// @nodoc

class _UpdatePickupLocationWithPlaceId implements PassengerRouteEvent {
  const _UpdatePickupLocationWithPlaceId(this.placeId);

  final String placeId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatePickupLocationWithPlaceId &&
            (identical(other.placeId, placeId) || other.placeId == placeId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, placeId);

  @override
  String toString() {
    return 'PassengerRouteEvent.updatePickUpLocationWithPlaceId(placeId: $placeId)';
  }
}

/// @nodoc

class _AddOrUpdateDropoffLocationWithPlaceId implements PassengerRouteEvent {
  const _AddOrUpdateDropoffLocationWithPlaceId({
    required this.placeId,
    this.index,
  });

  final String placeId;
  final int? index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AddOrUpdateDropoffLocationWithPlaceId &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, placeId, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.addOrUpdateDropoffLocationWithPlaceId(placeId: $placeId, index: $index)';
  }
}
