import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvSecreteConfig {
  static final EnvSecreteConfig instance = EnvSecreteConfig._();
  EnvSecreteConfig._();

  String get googleMapAndroidApiKey =>
      dotenv.env['GOOGLE_MAPS_ANDROID_API_KEY'] ??
      'GOOGLE_MAPS_API_KEY_NOT_FOUND';

  String get googleMapIOSApiKey =>
      dotenv.env['GOOGLE_MAPS_ISO_API_KEY'] ?? 'GOOGLE_MAPS_API_KEY_NOT_FOUND';

  String get getGoogleMapApiKey {
    return defaultTargetPlatform == TargetPlatform.android
        ? googleMapAndroidApiKey
        : googleMapIOSApiKey;
  }

 String get locationIQApiKey  {
    final key = dotenv.env['LOCATION_IQ_API_KEY'];
    if (key == null) {
      throw "LOCATION_IQ_API_KEY not found";
    }
    return key;
  }
}
