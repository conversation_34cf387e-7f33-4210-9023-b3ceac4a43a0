import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/blocs/image_picker/image_picker_bloc.dart';
import 'package:safari_yatri/common/blocs/locale_cubit/locale_cubit.dart';
import 'package:safari_yatri/common/blocs/once_cubit/once_cubit.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/common/repositories/vehicle_repository.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/network/dio_client.dart';
import 'package:safari_yatri/core/router/navigation_service.dart';
import 'package:safari_yatri/common/repositories/direction_repository.dart';
import 'package:safari_yatri/core/services/polling/polling_observer.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/features/admin/core/di/admin_dependency_injection.dart';
import 'package:safari_yatri/features/admin/core/repositories/ride_shift_repository.dart';
import 'package:safari_yatri/features/auth/blocs/change_password/change_password_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/forget_password/forget_password_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/password_toggle_cubit/password_toggle_cubit.dart';
import 'package:safari_yatri/features/auth/blocs/resend_otp/resend_opt_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/user_login/user_login_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/user_register/user_register_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/verify_otp/verify_otp_bloc.dart';
import 'package:safari_yatri/features/auth/repositories/auth_repository.dart';
import 'package:safari_yatri/features/booking/blocs/accept_passenger_request/accept_passenger_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/accept_rider_request/accept_rider_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/clear_my_cart/clear_my_cart_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_accepting_riders/get_my_accepting_riders_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_passengers_booking/get_my_current_passengers_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_passengers_booking.dart/get_my_passengers_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/polylines_points/polylines_points_bloc.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/repositories/request_for_driver_repository.dart';
import 'package:safari_yatri/features/driver/ride/ride_start_completor_bloc/ride_start_completor_bloc.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location/location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';
import 'package:safari_yatri/features/location/blocs/places/places_bloc.dart';
import 'package:safari_yatri/features/location/repositories/places_repository.dart';
import 'package:safari_yatri/features/location/repositories/remote_location_repository.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/terminate_account/terminate_account_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/update_my_profile/update_my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/upload_profile_image/upload_profile_image_bloc.dart';
import 'package:safari_yatri/features/my_profile/repositories/my_profile_repository.dart';
import 'package:safari_yatri/features/passenger/core/blocs/is_coming_from_draggable/is_coming_from_draggable_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_booking/get_my_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_booking/get_my_current_booking_bloc.dart';
import 'package:safari_yatri/features/role/blocs/get_user_role/get_user_role_bloc.dart';
import 'package:safari_yatri/features/role/repositories/role_repository.dart';

import '../../common/blocs/ride_notify_alert/ride_notify_alert_bloc.dart';

final sl = GetIt.instance;

Future<void> setupServiceLocator() async {
  ///blocs
  sl.registerLazySingleton(() => LocationBloc(remoteLocationRepository: sl()));
  sl.registerLazySingleton(() => LocaleCubit());
  sl.registerLazySingleton(() => IsComingFromDraggableCubit());
  sl.registerLazySingleton(() => LocationPermissionBloc(repo: sl()));
  sl.registerLazySingleton(() => PickupLocationPickingStatusCubit());
  sl.registerLazySingleton(() => UserRegisterBloc(repo: sl()));
  sl.registerLazySingleton(() => UserLoginBloc(repo: sl()));
  sl.registerLazySingleton(() => ResendOptBloc(repo: sl()));
  sl.registerLazySingleton(() => ForgetPasswordBloc(repo: sl()));

  sl.registerFactory(() => CurrentLocationBloc(repo: sl()));
  sl.registerLazySingleton(() => CurrentLatLngAddressBloc(repo: sl()));
  sl.registerLazySingleton(() => PasswordToggleCubit());
  sl.registerLazySingleton(() => OnceCubit());
  sl.registerLazySingleton(() => VerifyOtpBloc(repo: sl()));
  sl.registerLazySingleton(() => ChangePasswordBloc(repo: sl()));
  sl.registerLazySingleton(() => ClearMyCartBloc(repo: sl()));
  sl.registerLazySingleton(() => RideNotifyAlertBloc());
  sl.registerLazySingleton(
    () => PassengerRouteBloc(
      repo: sl(),
      directionRepository: sl(),
      placesRepository: sl(),
    ),
  );
  sl.registerLazySingleton(() => MapMovementCubit());
  sl.registerLazySingleton(() => LocalUserModeCubit());
  sl.registerLazySingleton(() => CurrentLocationNavigatorCubit(repo: sl()));

  sl.registerLazySingleton(() => GetRemoteUserRoleBloc(roleRepository: sl()));

  sl.registerFactory(() => ImagePickerBloc());
  sl.registerLazySingleton(() => PlacesBloc(repo: sl()));
  sl.registerLazySingleton(() => RequestForDriverBloc(repo: sl()));

  //MyProfile
  sl.registerLazySingleton(() => MyProfileBloc(myProfileRepository: sl()));

  sl.registerLazySingleton(() => UpdateMyProfileBloc(repo: sl()));

  sl.registerLazySingleton(() => TerminateAccountBloc(repo: sl()));
  sl.registerLazySingleton(() => GetVehicleTypeBloc(repo: sl()));

  sl.registerLazySingleton(() => UploadProfileImageBloc(repo: sl()));

  sl.registerLazySingleton(
    () => AddToCartBookingBloc(
      repo: sl(),
      rideShiftRepo: sl(),
      vehicleTypeRepo: sl(),
    ),
  );
  sl.registerLazySingleton(() => GetRideShiftBloc(repo: sl()));
  sl.registerFactory(() => RideStartCompletorBloc(repo: sl()));

  sl.registerLazySingleton(
    () => GetNewPassengerBloc(pollingService: sl(), bookingRepository: sl()),
  );

  sl.registerLazySingleton(
    () => PolylinesPointsBloc(
      directionRepository: sl(),
      locationRepository: sl(),
    ),
  );
  sl.registerLazySingleton(() => AcceptPassengerRequestBloc(repo: sl()));
  sl.registerLazySingleton(
    () => GetMyAcceptingRidersBloc(repo: sl(), pollingService: sl()),
  );
  sl.registerLazySingleton(
    () => GetMyCurrentPassengerBooking(repo: sl(), pollingService: sl()),
  );

  sl.registerLazySingleton(() => AcceptRiderRequestBloc(repo: sl()));
  sl.registerLazySingleton(
    () => GetMyCurrentBookingBloc(repo: sl(), polling: sl()),
  );

  sl.registerFactory(() => CancelRequestBloc(repo: sl()));

  sl.registerFactory(() => GetMyBookingBloc(repo: sl()));

  sl.registerLazySingleton(() => GetMyPassengersBookingBloc(repo: sl()));
  sl.registerLazySingleton(() => AppThemeCubit());

  ///***************repositories************

  sl.registerLazySingleton<RideShiftRepository>(
    () => RideShiftRepositoryI(apiService: sl()),
  );
  sl.registerLazySingleton<BookingRepository>(
    () => BookingRepositoryI(apiService: sl()),
  );
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryI(apiService: sl()),
  );

  sl.registerLazySingleton<MyProfileRepository>(
    () => MyProfileRepositoryImpl(apiService: sl()),
  );

  sl.registerLazySingleton<LocationRepository>(() => LocationRepositoryI());

  sl.registerLazySingleton<DioClient>(() => DioClient());
  // sl.registerLazySingleton<DirectionRepository>(
  //   () => GoogleDirectionRepository(dio: sl()),
  // );

  sl.registerLazySingleton<DirectionRepository>(
    () => LocationIQDirectionRepository(dio: sl()),
  );

  sl.registerLazySingleton<ApiService>(() => ApiService(sl<Dio>()));
  sl.registerLazySingleton<NavigationService>(() => NavigationService());

  sl.registerLazySingleton<RemoteLocationRepository>(
    () => RemoteLocationRepositoryI(apiService: sl()),
  );
  sl.registerLazySingleton<RoleRepository>(
    () => RoleRepositoryI(apiService: sl()),
  );

  sl.registerLazySingleton<PlacesRepository>(
    ///TODO: need to remove with Google
    () => LocationIQPlacesRepository(dio: sl()),
  );

  sl.registerLazySingleton<RequestForDriverRepository>(
    () => RequestForDriverRepositoryI(apiService: sl()),
  );

  sl.registerLazySingleton<VehicleRepository>(
    () => VehicleRepositoryI(apiService: sl()),
  );

  ///Polling services
  sl.registerSingleton<PollingService>(PollingService());

  sl.registerLazySingleton<PollingObserverService>(
    () => PollingObserverService(sl<PollingService>()),
  );

  await setupAdminDependencies();

  ///External services
  sl.registerLazySingleton<Dio>(() => sl<DioClient>().dio);
}

Future<void> resetServiceLocator() async {
  await sl.reset();
  await setupServiceLocator();
}
