import 'package:safari_yatri/common/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class CacheService {
  CacheService._internal();
  static final CacheService instance = CacheService._internal();
  late final FlutterSecureStorage _secureStorage;

  /// Keys for secure storage
  static const _keyAuthToken = 'auth_token';
  static const _keyAuthTokenExpiry = 'auth_token_expiry';
  static const _keyRefreshToken = 'refresh_token';

  Future<void> init() async {
    try {
      _secureStorage = const FlutterSecureStorage();
    } catch (e, stack) {
      dLog.d('CacheService init failed: $e\n$stack');
    }
  }

  // ===== Secure Storage methods =====

  /// Save auth token and its expiry timestamp
  Future<void> setAuthToken(
    String newToken,
    DateTime expiry, {
    required String? refreshToken,
  }) async {
    try {
      // Write token
      await _secureStorage.write(key: _keyAuthToken, value: newToken);
      // Write expiry as ISO-8601 string
      await _secureStorage.write(
        key: _keyAuthTokenExpiry,
        value: expiry.toIso8601String(),
      );
      // Optionally store refresh token
      if (refreshToken != null) {
        await _secureStorage.write(key: _keyRefreshToken, value: refreshToken);
      }
    } catch (e) {
      dLog.d('Error saving auth token: $e');
    }
  }

  ///TIme
  ///
  Future<DateTime?> getAuthTokenExpiry() async {
    try {
      final expiryStr = await _secureStorage.read(key: _keyAuthTokenExpiry);
      if (expiryStr == null) return null;
      return DateTime.tryParse(expiryStr);
    } catch (e) {
      dLog.d('Error reading auth-token expiry: $e');
      return null;
    }
  }

  /// Get auth token only if not expired. Returns `null` otherwise.
  Future<String?> getAuthToken() async {
    try {
      final token = await _secureStorage.read(key: _keyAuthToken);
      final expiryStr = await _secureStorage.read(key: _keyAuthTokenExpiry);
      if (token == null || expiryStr == null) return null;

      final expiry = DateTime.tryParse(expiryStr);
      if (expiry == null) {
        // Corrupt expiry data—clear both and return null
        await clearTokenData();
        return null;
      }

      if (DateTime.now().isAfter(expiry)) {
        // Token expired
        dLog.d('Auth token expired at $expiry, clearing stored token.');
        await clearTokenData();
        return null;
      }

      return token;
    } catch (e) {
      dLog.d('Error reading auth token: $e');
      return null;
    }
  }

  /// Clear token and expiry
  Future<void> clearTokenData() async {
    
    try {
      await _secureStorage.delete(key: _keyAuthToken);
      await _secureStorage.delete(key: _keyAuthTokenExpiry);
      await _secureStorage.delete(key: _keyRefreshToken);
    } catch (e) {
      dLog.d('Error clearing auth token data: $e');
    }
  }

  /// Get stored refresh token (if any)
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _keyRefreshToken);
    } catch (e) {
      dLog.d('Error reading refresh token: $e');
      return null;
    }
  }

  Future<void> setUserPhoneNumber(String phone) async {
    try {
      await _secureStorage.write(key: 'user_phone', value: phone);
    } catch (e) {
      dLog.d('Error saving phone number: $e');
    }
  }

  Future<String?> getUserPhoneNumber() async {
    try {
      return await _secureStorage.read(key: 'user_phone');
    } catch (e) {
      dLog.d('Error reading phone number: $e');
      return null;
    }
  }

  
}
