import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';

class AppMapHelper {
  /// Generates markers from a list of [LatLng] points.
  ///
  /// [points]: The list of LatLng points for which to generate markers.
  /// [pickupIcon]: Optional custom BitmapDescriptor for the pickup marker (first point).
  /// [destinationIcon]: Optional custom BitmapDescriptor for the destination marker (last point).
  /// [stopIcon]: Optional custom BitmapDescriptor for intermediate stop markers.
  /// [stopNames]: Optional map where key is the index of the stop (1-based) and value is its name.
  static Future<Set<Marker>> generateRouteMarkers(
    List<LatLng> points, {
    BitmapDescriptor? pickupIcon,
    BitmapDescriptor? destinationIcon,
    BitmapDescriptor? stopIcon,
    Map<int, String>? stopNames,
  }) async {
    if (points.isEmpty) return {};
    Set<Marker> markers = {};

    List<String> mapPickupImages = [
      ImageConstant.mapDestination,
      ImageConstant.mapPickUp1,
      ImageConstant.mapPickUp2,
      ImageConstant.mapPickUp3,
      ImageConstant.mapPickUp4,
      // ImageConstant.mapPickUp5,
    ];

    for (int i = 0; i < points.length; i++) {
      String markerImage;

      if (i == 0) {
        markerImage = ImageConstant.mapPickUp;
      } else if (i == points.length - 1) {
        markerImage = ImageConstant.mapDestination;
      } else if (i < mapPickupImages.length) {
        markerImage = mapPickupImages[i];
      } else {
        markerImage = ImageConstant.mapPickUp1;
      }

      markers.add(
        Marker(
          markerId: MarkerId('marker_$i'),
          position: LatLng(points[i].latitude, points[i].longitude),
          icon: await BitmapDescriptor.asset(
            const ImageConfiguration(size: Size(100, 100)),
            markerImage,
            height: 30,
            width: 30,
          ),
          infoWindow: InfoWindow(
            title:
                i == 0
                    ? 'Pickup Location'
                    : (i == points.length - 1
                        ? 'Final Destination'
                        : 'Stop $i'),
          ),
        ),
      );
    }

    return markers;
  }

  /// Creates a polyline from a list of [LatLng] points.
  ///
  /// [points]: The list of LatLng points forming the polyline.
  /// [color]: The color of the polyline. Defaults to a dark grey.
  /// [width]: The width of the polyline. Defaults to 4.
  /// [jointType]: The joint type for the polyline. Defaults to [JointType.round].
  /// [startCap]: The cap for the start of the polyline. Defaults to [Cap.buttCap].
  /// [endCap]: The cap for the end of the polyline. Defaults to [Cap.buttCap].
  static Set<Polyline> createPolyline(
    List<LatLng> points, {
    Color color = AppColors.brandGreen,
    int width = 4,
    JointType jointType = JointType.round,
    Cap startCap = Cap.buttCap,
    Cap endCap = Cap.buttCap,
    String polylineId = 'route',
  }) {
    if (points.length < 2) return {};

    return {
      Polyline(
        polylineId: PolylineId(polylineId),
        color: color,
        width: width,
        points: points,
        jointType: jointType,
        startCap: startCap,
        endCap: endCap,
      ),
    };
  }

  /// Animates polyline drawing over time.
  ///
  /// [fullRoute]: The complete list of LatLng points for the route.
  /// [onUpdate]: Callback function that receives the current animated portion of the route.
  /// [onComplete]: Callback function when the animation finishes.
  /// [interval]: The time interval between drawing each segment.
  ///
  /// Returns a [StreamSubscription<List<LatLng>>] which can be used to
  /// pause, resume, or cancel the animation.
  static StreamSubscription<List<LatLng>> animatePolyline({
    required List<LatLng> fullRoute,
    required ValueSetter<List<LatLng>> onUpdate,
    required VoidCallback onComplete,
    Duration interval = const Duration(milliseconds: 150),
  }) {
    final StreamController<List<LatLng>> controller =
        StreamController<List<LatLng>>();
    Timer? timer;
    int index = 0;

    void startTimer() {
      timer = Timer.periodic(interval, (t) {
        if (index < fullRoute.length) {
          final currentPoints = fullRoute.sublist(0, index + 1);
          controller.add(currentPoints);
          onUpdate(currentPoints);
          index++;
        } else {
          timer?.cancel();
          controller.close();
          onComplete();
        }
      });
    }

    startTimer();

    return controller.stream.listen(null) // Listen to the stream
      ..onDone(() {
        // Optional: Perform actions when stream is closed (animation complete)
      })
      ..onError((error) {
        // Optional: Handle errors if any occur in the stream
      });
  }

  /// Zooms camera to fit all given [LatLng] points.
  ///
  /// [controller]: The GoogleMapController instance.
  /// [points]: The list of LatLng points to fit within the camera bounds.
  /// [padding]: Padding around the bounds in pixels. Defaults to 50.
  /// [minZoom]: Optional minimum zoom level to prevent excessive zooming in.
  /// [maxZoom]: Optional maximum zoom level to prevent excessive zooming out.
  static Future<void> zoomToFit({
    required GoogleMapController mapController,
    required List<LatLng> points,
    double padding = 50,
    double? minZoom,
    double? maxZoom,
  }) async {
    if (points.isEmpty) return;

    if (points.length == 1) {
      // If only one point, animate to that point with a default zoom level
      await mapController.animateCamera(
        CameraUpdate.newLatLngZoom(
          points.first,
          15.0,
        ), // Default zoom for single point
      );
      return;
    }

    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final location in points) {
      minLat = min(minLat, location.latitude);
      maxLat = max(maxLat, location.latitude);
      minLng = min(minLng, location.longitude);
      maxLng = max(maxLng, location.longitude);
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    await mapController.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, kMapPadding),
    );
  }

  static Future<Set<Marker>> generateMarkerCurrentDriverToPassengerPickup(
    List<LatLng> points,
  ) async {
    if (points.isEmpty) return {};
    Set<Marker> markers = {};

    for (int i = 0; i < points.length; i++) {
      String markerImage;

      if (i == 0) {
        markerImage = ImageConstant.safariImage;
      } else {
        markerImage = ImageConstant.mapPickUp;
      }

      markers.add(
        Marker(
          markerId: MarkerId('marker_$i'),
          position: LatLng(points[i].latitude, points[i].longitude),
          icon: await BitmapDescriptor.asset(
            const ImageConfiguration(size: Size(100, 100)),
            markerImage,
            height: 30,
            width: 30,
          ),
          infoWindow: InfoWindow(
            title: i == 0 ? 'Current Location' : 'Final Destination',
          ),
        ),
      );
    }

    return markers;
  }
}
