// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/features/booking/models/booking_details.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';

class TripBookingCart extends StatelessWidget {
  const TripBookingCart({super.key, required this.booking});
  final BookingModel booking;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _showBookingDetails(context, booking),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with booking ID and status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            '#${booking.bookingId}',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              booking.serviceStatus,
                            ).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            booking.serviceStatus.toUpperCase(),
                            style: TextStyle(
                              color: _getStatusColor(booking.serviceStatus),
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Icon(
                      booking.isSharedBookingMode ? Icons.people : Icons.person,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Route information
                if (booking.bookingDetailViews.isNotEmpty)
                  _buildRouteInfo(booking.bookingDetailViews.first),

                const SizedBox(height: 16),

                // Trip details row
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        Icons.calendar_today,
                        'Date',
                        _formatDate(booking.bookingDate),
                      ),
                    ),
                    Expanded(
                      child: _buildDetailItem(
                        Icons.straighten,
                        'Distance',
                        formatDistance(booking.totalDistanceInMeter),
                      ),
                    ),
                    Expanded(
                      child: _buildDetailItem(
                        Icons.people,
                        'Passengers',
                        booking.passengerCount.toString(),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Fare and rider info
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fare Amount',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Rs. ${booking.acceptedFareAmount.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    if (booking.riderName.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Rider',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 12,
                                backgroundColor: Colors.blue[100],
                                child: Icon(
                                  Icons.person,
                                  size: 16,
                                  color: Colors.blue[700],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                booking.riderName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),

                // Payment status
                if (booking.paymentStatus.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.payment,
                        size: 16,
                        color: _getPaymentStatusColor(booking.paymentStatus),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Payment: ${booking.paymentStatus}',
                        style: TextStyle(
                          color: _getPaymentStatusColor(booking.paymentStatus),
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget _buildRouteInfo(BookingDetailModel detail) {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.grey[50],
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      children: [
        Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                detail.sourceAddress.isNotEmpty
                    ? detail.sourceAddress
                    : 'Pick up location',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Container(
              width: 2,
              height: 20,
              color: Colors.grey[300],
              margin: const EdgeInsets.only(left: 3),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                detail.destinationAddress.isNotEmpty
                    ? detail.destinationAddress
                    : 'Drop off location',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildDetailItem(IconData icon, String label, String value) {
  return Column(
    children: [
      Icon(icon, size: 16, color: Colors.grey[600]),
      const SizedBox(height: 4),
      Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 10)),
      const SizedBox(height: 2),
      Text(
        value,
        style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
      ),
    ],
  );
}

void _showBookingDetails(BuildContext context, BookingModel booking) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => _buildBookingDetailsSheet(booking),
  );
}

Widget _buildBookingDetailsSheet(BookingModel booking) {
  return DraggableScrollableSheet(
    initialChildSize: 0.7,
    minChildSize: 0.5,
    maxChildSize: 0.95,
    builder: (context, scrollController) {
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Booking Details',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                padding: const EdgeInsets.all(20),
                child: _buildDetailedBookingInfo(booking),
              ),
            ),
          ],
        ),
      );
    },
  );
}

Widget _buildDetailedBookingInfo(BookingModel booking) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Status and booking info
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getStatusColor(booking.serviceStatus).withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getStatusColor(booking.serviceStatus).withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getStatusIcon(booking.serviceStatus),
              color: _getStatusColor(booking.serviceStatus),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    booking.serviceStatus.toUpperCase(),
                    style: TextStyle(
                      color: _getStatusColor(booking.serviceStatus),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'Booking #${booking.bookingId}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),

      const SizedBox(height: 20),

      // Detailed information sections
      _buildInfoSection('Trip Information', [
        _buildInfoRow('Booking Date', _formatDate(booking.bookingDate)),
        _buildInfoRow('Start Date', _formatDate(booking.bookingStartDate)),
        _buildInfoRow(
          'Trip Type',
          booking.isSharedBookingMode ? 'Shared Ride' : 'Private Ride',
        ),
        _buildInfoRow('Distance', formatDistance(booking.totalDistanceInMeter)),
        _buildInfoRow('Passengers', booking.passengerCount.toString()),
      ]),

      const SizedBox(height: 20),

      // People information
      _buildInfoSection('People', [
        _buildInfoRow(
          'Passenger',
          booking.passengerName.isNotEmpty ? booking.passengerName : 'N/A',
        ),
        _buildInfoRow(
          'Rider',
          booking.riderName.isNotEmpty ? booking.riderName : 'N/A',
        ),
      ]),

      const SizedBox(height: 20),

      // Payment information
      _buildInfoSection('Payment', [
        _buildInfoRow(
          'Total Fare',
          'Rs. ${booking.totalFareAmount.toStringAsFixed(2)}',
        ),
        _buildInfoRow(
          'Accepted Fare',
          'Rs. ${booking.acceptedFareAmount.toStringAsFixed(2)}',
        ),
        _buildInfoRow(
          'Payment Status',
          booking.paymentStatus.isNotEmpty ? booking.paymentStatus : 'N/A',
        ),
      ]),

      // Cancellation info if cancelled
      if (booking.bookingCancelDate.isNotEmpty) ...[
        const SizedBox(height: 20),
        _buildInfoSection('Cancellation', [
          _buildInfoRow(
            'Cancelled Date',
            _formatDate(booking.bookingCancelDate),
          ),
          _buildInfoRow(
            'Cancelled By',
            booking.cancelUserName.isNotEmpty ? booking.cancelUserName : 'N/A',
          ),
          _buildInfoRow(
            'Reason',
            booking.reasonForCancel.isNotEmpty
                ? booking.reasonForCancel
                : 'N/A',
          ),
        ]),
      ],

      // Route details
      if (booking.bookingDetailViews.isNotEmpty) ...[
        const SizedBox(height: 20),
        _buildRouteSection(booking.bookingDetailViews),
      ],
    ],
  );
}

Widget _buildInfoSection(String title, List<Widget> children) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 12),
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(children: children),
      ),
    ],
  );
}

Widget _buildRouteSection(List<BookingDetailModel> routes) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text(
        'Route Details',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 12),
      ...routes.asMap().entries.map((entry) {
        int index = entry.key;
        BookingDetailModel route = entry.value;
        return _buildRouteItem(route, index);
      }),
    ],
  );
}

Widget _buildRouteItem(BookingDetailModel route, int index) {
  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.grey[50],
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      children: [
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: index == 0 ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    index == 0 ? 'Pick Up' : 'Drop Off',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    index == 0 ? route.sourceAddress : route.destinationAddress,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildInfoRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 6),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
          ),
        ),
      ],
    ),
  );
}

Color _getStatusColor(String status) {
  switch (status.toLowerCase()) {
    case 'completed':
      return Colors.green;
    case 'cancelled':
      return Colors.red;
    case 'ongoing':
    case 'active':
      return Colors.blue;
    case 'pending':
      return Colors.orange;
    default:
      return Colors.grey;
  }
}

IconData _getStatusIcon(String status) {
  switch (status.toLowerCase()) {
    case 'completed':
      return Icons.check_circle;
    case 'cancelled':
      return Icons.cancel;
    case 'ongoing':
    case 'active':
      return Icons.directions_car;
    case 'pending':
      return Icons.pending;
    default:
      return Icons.info;
  }
}

Color _getPaymentStatusColor(String status) {
  switch (status.toLowerCase()) {
    case 'paid':
      return Colors.green;
    case 'pending':
      return Colors.orange;
    case 'failed':
      return Colors.red;
    default:
      return Colors.grey;
  }
}

String _formatDate(String dateString) {
  if (dateString.isEmpty) return 'N/A';
  try {
    DateTime date = DateTime.parse(dateString);
    return DateFormat('MMM dd, yyyy - hh:mm a').format(date);
  } catch (e) {
    return dateString;
  }
}
