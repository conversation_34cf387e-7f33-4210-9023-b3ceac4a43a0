import 'dart:async';
import 'package:flutter/material.dart';

class AnimatedTextLoader extends StatefulWidget {
  final List<String> messages;
  final Duration switchDuration;
  final TextStyle? textStyle;

  const AnimatedTextLoader({
    super.key,
    required this.messages,
    this.switchDuration = const Duration(seconds: 2),
    this.textStyle,
  });

  @override
  State<AnimatedTextLoader> createState() => _AnimatedTextLoaderState();
}

class _AnimatedTextLoaderState extends State<AnimatedTextLoader> {
  late int _currentIndex;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _currentIndex = 0;

    _timer = Timer.periodic(widget.switchDuration, (timer) {
      setState(() {
        _currentIndex = (_currentIndex + 1) % widget.messages.length;
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      transitionBuilder:
          (child, animation) =>
              FadeTransition(opacity: animation, child: child),
      child: Text(
        widget.messages[_currentIndex],
        key: ValueKey(widget.messages[_currentIndex]),
        style: widget.textStyle ?? Theme.of(context).textTheme.headlineSmall,
      ),
    );
  }
}
