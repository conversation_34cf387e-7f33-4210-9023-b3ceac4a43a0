import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart' as t;

class BottomSheetPage<T> extends Page<T> {
  final Offset? anchorPoint;
  final Color? barrierColor;
  final bool barrierDismissible;
  final String? barrierLabel;
  final bool useSafeArea;
  final CapturedThemes? themes;
  final WidgetBuilder builder;
  final double heightFactor;
  final bool showDragIcon;
  final bool isScrollControlled;

  const BottomSheetPage({
    required this.builder,
    this.anchorPoint,
    this.barrierColor,
    this.barrierDismissible = true,
    this.barrierLabel,
    this.useSafeArea = true,
    this.themes,
    this.heightFactor = 0.93,
    this.showDragIcon = true,
    this.isScrollControlled = true,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  @override
  Route<T> createRoute(BuildContext context) => ModalBottomSheetRoute<T>(
    settings: this,
    isDismissible: barrierDismissible,
    showDragHandle: showDragIcon,
    constraints: BoxConstraints.loose(
      Size.fromHeight(MediaQuery.sizeOf(context).height * heightFactor),
    ),
    builder:
        (context) => builder(context),
    anchorPoint: anchorPoint,
    barrierLabel: barrierLabel,
    useSafeArea: useSafeArea,
    isScrollControlled: isScrollControlled,
    backgroundColor: t.T.c.surfaceContainerLowest,

    clipBehavior: Clip.antiAlias,
  );
}
