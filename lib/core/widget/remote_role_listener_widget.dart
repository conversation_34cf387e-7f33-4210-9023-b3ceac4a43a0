import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:safari_yatri/core/constant/user_roles.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/features/role/blocs/get_user_role/get_user_role_bloc.dart';

///Helps us to navigate according to role
///when user logins or user register for first time
class RoleBaseNavigatorPage extends StatefulWidget {
  const RoleBaseNavigatorPage({super.key});

  @override
  State<RoleBaseNavigatorPage> createState() => _RoleBaseNavigatorPageState();
}

class _RoleBaseNavigatorPageState extends State<RoleBaseNavigatorPage> {
  @override
  void initState() {
    super.initState();
    sl<GetRemoteUserRoleBloc>().add(GetRemoteUserRoleEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<GetRemoteUserRoleBloc, GetRemoteUserRoleState>(
        listenWhen: (previous, current) => previous != current,
        listener: (context, state) {
          state.whenOrNull(
            failure: (f) {
              context.goNamed(AppRoutesName.passengerHome);
            },
            loaded: (role) {
              sl<GetRemoteUserRoleBloc>().add(GetRemoteUserRoleEvent.get());

              if (role == kRiderRole) {
                context.goNamed(AppRoutesName.driverHome);
              } else if (role == kAdminRole) {
                context.goNamed(AppRoutesName.adminDashboard);
              } else {
                context.goNamed(AppRoutesName.passengerHome);
              }
            },
          );
        },
        builder: (context, state) {
          return const Center(
            child: CircularProgressIndicator(), // or use shimmer here
          );
        },
      ),
    );
  }
}
