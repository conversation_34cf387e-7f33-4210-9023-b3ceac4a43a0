import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/core/constant/map_style.dart';

class CustomGoogleMap extends StatefulWidget {
  final CameraPosition initialCameraPosition;
  final Set<Marker> markers;
  final Set<Polyline> polylines;
  final bool myLocationEnabled;
  final bool zoomControlsEnabled;
  final bool myLocationButtonEnabled;
  final bool mapToolbarEnabled;
  final MapType mapType;
  final String? mapStyle;
  final EdgeInsets? padding;
  final double? minZoom;
  final double? maxZoom;

  // Callbacks
  final void Function(GoogleMapController)? onMapCreated;
  final void Function()? onCameraMoveStarted;
  final void Function()? onCameraIdle;
  final void Function(CameraPosition)? onCameraMove;
  final void Function(LatLng)? onMapTap;
  final void Function(MarkerId)? onMarkerTap;

  const CustomGoogleMap({
    super.key,
    required this.initialCameraPosition,
    this.markers = const {},
    this.polylines = const {},
    this.myLocationEnabled = false,
    this.zoomControlsEnabled = false,
    this.myLocationButtonEnabled = false,
    this.mapToolbarEnabled = false,
    this.mapType = MapType.normal,
    this.mapStyle,
    this.padding,
    this.minZoom,
    this.maxZoom,
    this.onMapCreated,
    this.onCameraMoveStarted,
    this.onCameraIdle,
    this.onCameraMove,
    this.onMapTap,
    this.onMarkerTap,
  });

  @override
  State<CustomGoogleMap> createState() => _CustomGoogleMapState();
}

class _CustomGoogleMapState extends State<CustomGoogleMap> {
  GoogleMapController? _controller;

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  void _handleMapCreated(GoogleMapController controller) async {
    _controller = controller;
    widget.onMapCreated?.call(controller);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppThemeCubit, AppThemeState>(
      builder: (context, state) {
        return GoogleMap(
          initialCameraPosition: widget.initialCameraPosition,
          markers: widget.markers,
          style: state.mode == ThemeMode.dark ? kMapDarkStyle : kMapLightStyle,
          polylines: widget.polylines,
          myLocationEnabled: widget.myLocationEnabled,
          myLocationButtonEnabled: widget.myLocationButtonEnabled,
          zoomControlsEnabled: widget.zoomControlsEnabled,
          mapToolbarEnabled: widget.mapToolbarEnabled,
          mapType: widget.mapType,
          padding: widget.padding ?? EdgeInsets.zero,

          // Zoom Limits
          minMaxZoomPreference:
              (widget.minZoom != null && widget.maxZoom != null)
                  ? MinMaxZoomPreference(widget.minZoom!, widget.maxZoom!)
                  : MinMaxZoomPreference.unbounded,

          // Callbacks
          onMapCreated: _handleMapCreated,
          onCameraMoveStarted: widget.onCameraMoveStarted,
          onCameraIdle: widget.onCameraIdle,
          onCameraMove: widget.onCameraMove,
          onTap: widget.onMapTap,
        );
      },
    );
  }
}
