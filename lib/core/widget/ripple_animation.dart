import 'dart:math';

import 'package:flutter/material.dart';

class LayeredRippleEffect extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final bool isCircular;

  const LayeredRippleEffect({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 3),
    this.isCircular = true,
  });

  @override
  State<LayeredRippleEffect> createState() => _LayeredRippleEffectState();
}

class _LayeredRippleEffectState extends State<LayeredRippleEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _ripples;

  final List<Color> rippleColors = [
    Colors.green.shade100,
    Colors.green.shade200,
    Colors.green.shade300,
  ];

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(vsync: this, duration: widget.duration)
      ..repeat();

    _ripples = List.generate(
      rippleColors.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.2,
            min(1.0, index * 0.2 + 0.6),
            curve: Curves.easeOut,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      foregroundPainter: MultiRipplePainter(
        animations: _ripples,
        colors: rippleColors,
        isCircular: widget.isCircular,
      ),
      child: widget.child,
    );
  }
}

class MultiRipplePainter extends CustomPainter {
  final List<Animation<double>> animations;
  final List<Color> colors;
  final bool isCircular;

  MultiRipplePainter({
    required this.animations,
    required this.colors,
    required this.isCircular,
  }) : super(repaint: animations.first);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = sqrt(pow(size.width, 2) + pow(size.height, 2)) / 2;

    for (int i = 0; i < animations.length; i++) {
      final radius = animations[i].value * maxRadius;
      final opacity = (1 - animations[i].value).clamp(0.0, 1.0);
      final paint =
          Paint()
            ..color = colors[i].withOpacity(opacity)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 4;

      if (isCircular) {
        canvas.drawCircle(center, radius, paint);
      } else {
        final rect = Rect.fromCenter(
          center: center,
          width: radius * 2,
          height: radius * 2,
        );
        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, Radius.circular(16)),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
