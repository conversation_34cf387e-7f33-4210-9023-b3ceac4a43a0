import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';

/// Map picker is controlled with MapPickerController. Map pin is lifted up
/// whenever mapMoving() is called, and will be down when mapFinishedMoving()
/// is called. You can call disposeCallbacks() to release the listeners.
class AppMapPickerController {
  Function? mapMoving;
  Function? mapFinishedMoving;
  Function? stop;

  /// Manually dispose the assigned callbacks to avoid memory leaks
  void disposeCallbacks() {
    mapMoving = null;
    mapFinishedMoving = null;
    stop = null;
  }
}

/// MapPicker widget is main widget that gets map as a child.
/// It does not restrict user from using maps other than google map.
class AppMapPicker extends StatefulWidget {
  final Widget child;
  final Widget? iconWidget;
  final bool showDot;
  final AppMapPickerController mapPickerController;
  final bool showWidgetAboveDot;

  const AppMapPicker({
    super.key,
    required this.child,
    required this.mapPickerController,
    this.iconWidget,
    this.showDot = true,
    this.showWidgetAboveDot = true,
  });

  @override
  State<AppMapPicker> createState() => _MapPickerState();
}

class _MapPickerState extends State<AppMapPicker>
    with SingleTickerProviderStateMixin {
  static const double _dotRadius = 2.2;

  late final AnimationController animationController;
  late final Animation<double> translateAnimation;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    widget.mapPickerController.mapMoving = mapMoving;
    widget.mapPickerController.mapFinishedMoving = mapFinishedMoving;
    widget.mapPickerController.stop = stop;

    translateAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: animationController, curve: Curves.ease));
  }

  void mapMoving() {
    if (!animationController.isAnimating &&
        !animationController.isCompleted &&
        mounted) {
      animationController.forward();
    }
  }

  void mapFinishedMoving() {
    if (mounted) animationController.reverse();
  }

  /// Stops the animation and resets the state
  void stop() {
    if (mounted) {
      animationController.stop();
      animationController.reset();
    }
  }

  @override
  void dispose() {
    animationController.dispose();
    widget.mapPickerController.disposeCallbacks(); // prevent memory leaks
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          alignment: Alignment.center,
          children: [
            widget.child,
            Positioned(
              bottom: constraints.maxHeight * 0.5 - 10,
              child: AnimatedBuilder(
                animation: animationController,
                builder: (context, snapshot) {
                  return Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      if (widget.showDot)
                        BlocBuilder<MapMovementCubit, MapMovementState>(
                          builder: (context, state) {
                            return state == MapMovementState.stopped
                                ? SizedBox()
                                : Container(
                                  width: _dotRadius,
                                  height: _dotRadius,
                                  decoration: BoxDecoration(
                                    color: AppColors.brandGreen,
                                    borderRadius: BorderRadius.circular(
                                      _dotRadius, 
                                    ),
                                  ),
                                );
                          },
                        ),
                      Transform.translate(
                        offset: Offset(0, -15 * translateAnimation.value),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            widget.showWidgetAboveDot &&
                                    widget.iconWidget != null
                                ? ShowCurrentLatLngAddress()
                                : SizedBox.shrink(),
                            widget.iconWidget ?? SizedBox.shrink(),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}

class ShowCurrentLatLngAddress extends StatelessWidget {
  const ShowCurrentLatLngAddress({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MapMovementCubit, MapMovementState>(
      builder: (context, state) {
        return state == MapMovementState.moving
            ? SizedBox()
            : BlocBuilder<CurrentLatLngAddressBloc, CurrentLatLngAddressState>(
              builder: (context, state) {
                return state.maybeWhen(
                  loading:
                      () => SizedBox(
                        width: 10,
                        height: 10,
                        child: const CircularProgressIndicator(),
                      ),
                  loaded:
                      (data) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          data,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  orElse: () => SizedBox(),
                );
              },
            );
      },
    );
  }
}
