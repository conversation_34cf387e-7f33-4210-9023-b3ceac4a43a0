import 'package:flutter/material.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class CurrentLocationNavigatorButton extends StatelessWidget {
  const CurrentLocationNavigatorButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: T.c.surface,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: T.c.shadow.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: IconButton.filled(
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(T.c.surface),
          foregroundColor: WidgetStatePropertyAll(T.c.onSurface),
          shadowColor: WidgetStatePropertyAll(T.c.shadow),
        ),
        onPressed: () {
          sl<CurrentLocationNavigatorCubit>().getCurrentLocation();
        },
        icon: Icon(LucideIcons.locateFixed, color: T.c.onSurfaceVariant),
      ),
    );
  }
}
