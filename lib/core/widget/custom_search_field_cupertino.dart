import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart'; // For Icons and Colors

class CustomSearchBarWidget extends StatelessWidget {
  final String placeholder;
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool readOnly;
  final VoidCallback? onTap;
  final Widget? suffixIcon;
  final bool isPickUp;
  final Color? pickUpColor;
  final Color? destinationColor;

  const CustomSearchBarWidget({
    super.key,
    this.placeholder = 'Search',
    this.leadingIcon,
    this.trailingIcon,
    this.controller,
    this.onChanged,
    this.readOnly = false,
    this.onTap,
    this.suffixIcon,
    this.isPickUp = true,
    this.pickUpColor,
    this.destinationColor,
  });

  @override
  Widget build(BuildContext context) {
    final TextEditingController effectiveController =
        controller ?? TextEditingController();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      height: 45,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          if (leadingIcon != null)
            Icon(leadingIcon, color: CupertinoColors.systemGrey, size: 18),
          if (leadingIcon != null) const SizedBox(width: 8),
          Expanded(
            child: CupertinoTextField(
              controller: effectiveController,
              placeholder: placeholder,
              readOnly: readOnly,
              onTap: onTap,
              padding: const EdgeInsets.symmetric(vertical: 12),
              style: TextStyle(fontSize: 16, color: Colors.grey.shade800),
              onChanged: onChanged,
              decoration: null,
              suffix: suffixIcon,
              prefix: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder:
                    (child, anim) => ScaleTransition(scale: anim, child: child),
                child:
                    effectiveController.text.isNotEmpty
                        ? Icon(
                          Icons.radio_button_checked,
                          key: const ValueKey('focusedIcon'),
                          color:
                              isPickUp
                                  ? (pickUpColor ?? Colors.green)
                                  : (destinationColor ?? Colors.blue),
                          size: 20,
                        )
                        : Icon(
                          Icons.search,
                          key: const ValueKey('defaultIcon'),
                          color: CupertinoColors.systemGrey,
                          size: 20,
                        ),
              ),
            ),
          ),
          if (trailingIcon != null) const SizedBox(width: 8),
          if (trailingIcon != null)
            Icon(trailingIcon, color: CupertinoColors.systemGrey),
        ],
      ),
    );
  }
}
