import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../theme/app_styles.dart';

Builder buildDrawerButton(BuildContext context) {
  return Builder(
    builder: (context) {
      return Container(
        height: 45,
        width: 45,
        margin: EdgeInsets.all(AppStyles.space12),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: ColorScheme.of(context).surfaceContainerHighest,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: IconButton(
          icon: Icon(
            LucideIcons.menu,
            color: ColorScheme.of(context).inverseSurface.withAlpha(150),
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      );
    },
  );
}
