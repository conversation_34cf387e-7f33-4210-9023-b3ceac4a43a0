import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/constant/user_roles.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/services/driver_navigation_mode_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';
part 'local_user_mode_state.dart';

///yesle chaii k garxa vanda
///locally user ko mode lai save garxa
///like passenger hokii rider mode ho
///yesle server sanga kunaii sarokar gardaina
class LocalUserModeCubit extends Cubit<LocalUserModeState> {
  // final UserModeService _modeService;
  bool _isInitialized = false;
  String _userMode = kPassengerRole;
  // LocalUserModelCubit({required UserModeService modeService})
  //   : _modeService = modeService,
  //     super(const LocalUserModeState.initial());
  LocalUserModeCubit() : super(const LocalUserModeState.initial());

  /// Force switch to passenger mode
  Future<void> switchPassengerMode() async {
    try {
      emit(const LocalUserModeState.loading());
      await Future.delayed(const Duration(seconds: 1));

      final remoteRoleInstance = RemoteRoleService.instance;
      final remoteUserRole = remoteRoleInstance.getUserRole();
      if (remoteUserRole == kRiderRole) {
        ///user role rider vaye paxii switch garxa sakxa passenge rmaa locally
        await RiderLocallySwitchingModeServices.switchLocallyToPassenger();
      }
      _userMode = kPassengerRole;
      emit(LocalUserModeState.loaded(_userMode));
    } catch (e) {
      emit(
        LocalUserModeState.failure(UnexpectedFailure(message: e.toString())),
      );
    }
  }

  Future<void> switchDriverMode() async {
    try {
      emit(const LocalUserModeState.loading());

      await Future.delayed(const Duration(seconds: 1));
      final remoteRoleInstance = RemoteRoleService.instance;
      final remoteUserRole = remoteRoleInstance.getUserRole();

      final isPassengerRequestForRiderPending =
          remoteRoleInstance.isPassengerRequestForRiderPending();

      if (isPassengerRequestForRiderPending) {
        _userMode = kPendingRequestForRiderRole;
      } else if (remoteUserRole == kRiderRole) {
        ///rider chaii rider maii chadaii xa vane clear gardine
        await RiderLocallySwitchingModeServices.clear();
        _userMode = kRiderRole;
      } else {
        _userMode = kPassengerRole;
      }

      emit(LocalUserModeState.loaded(_userMode));
    } catch (e) {
      emit(LocalUserModeState.loaded(kPassengerRole));
    }
  }

  /// Initialize user mode on startup (only once)
  Future<void> getUserMode() async {
    if (_isInitialized) return;
    _isInitialized = true;
    emit(LocalUserModeState.loaded(_userMode));
  }
}
