// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_ride_shift_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetRideShiftEvent {
  bool? get includeBlocked;
  bool? get refetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetRideShiftEvent &&
            (identical(other.includeBlocked, includeBlocked) ||
                other.includeBlocked == includeBlocked) &&
            (identical(other.refetch, refetch) || other.refetch == refetch));
  }

  @override
  int get hashCode => Object.hash(runtimeType, includeBlocked, refetch);

  @override
  String toString() {
    return 'GetRideShiftEvent(includeBlocked: $includeBlocked, refetch: $refetch)';
  }
}

/// @nodoc

class _Get implements GetRideShiftEvent {
  const _Get([this.includeBlocked, this.refetch]);

  @override
  final bool? includeBlocked;
  @override
  final bool? refetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Get &&
            (identical(other.includeBlocked, includeBlocked) ||
                other.includeBlocked == includeBlocked) &&
            (identical(other.refetch, refetch) || other.refetch == refetch));
  }

  @override
  int get hashCode => Object.hash(runtimeType, includeBlocked, refetch);

  @override
  String toString() {
    return 'GetRideShiftEvent.get(includeBlocked: $includeBlocked, refetch: $refetch)';
  }
}
