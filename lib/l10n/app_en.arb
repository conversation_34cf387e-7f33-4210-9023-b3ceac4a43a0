{"@@locale": "en", "appname": "Safari Yatri", "welcomeback": "Welcome Back", "signintocontinue": "Sign in to continue", "email": "Email", "password": "Password", "signin": "Sign In", "signup": "Sign Up", "donthaveaccount": "Don't have an account? Sign Up", "selectedLanguage": "Select Language", "chooseLater": "You can change language later from settings", "next": "Next", "skip": "<PERSON><PERSON>", "done": "Done", "onBoardingfirstScreenTitle": "One app for all services.", "onBoardingSecondScreenTitle": "Get there, on time", "onBoardingThirdScreenTitle": "Pay, as you want.", "onBoardingfirstScreenDesc": "Get a ride with your finertips.", "onBoardingSecondScreenDesc": "Best the traffic and reach your destination fast, every time.", "onBoardingThirdScreenDesc": "Cash? Card? Wallet? We accept it all. Let's get started.", "welcomeText": "Welcome", "welcomeDescription": "Have a better sharing exprience", "locationPermissionTitle": "Enable Location Permission", "locationPermissionDescription": "To get great service you need to provide location permission. You Can always change permission from settings.", "enableLocation": "Enable Location", "locationEnableAccess": "Enable Location Access", "locationRequestingPermission": "Requesting Permission...", "locationAccessGranted": "Location Access Granted", "locationNeeded": "Location Needed", "locationDescription": "This app needs your location to connect riders with drivers and help passengers track trips in real time.", "locationWaitingConfirmation": "Waiting for your confirmation in the permission dialog.", "locationRedirecting": "Great! We've got your location. Redirecting you now…", "locationDenied": "You denied location access. Tap below to try again.", "locationPermanentlyDenied": "Location permanently denied. Please open settings to allow access.", "locationRequired": "Location permission is required to continue.", "locationEnableButton": "Enable Location", "locationOpenSettings": "Open App Settings", "locationTryAgain": "Try Again", "locationGoToSettings": "Go to Settings", "locationAllowAccess": "Allow Location Access", "locationPermissionHeader": "Location Permission Required", "locationServiceDisabled": "Location Services Disabled", "locationServiceDisabledMessage": "Location services are turned off on your device. Please enable them to continue.", "openLocationSettings": "Open Location Settings", "showCaseDrawerDescription": "View your profile and settings", "showCasePassengerCoutDescription": "Select the number of passengers", "showCasePickUpLocationDescription": "Select your pickup location", "showCaseDestinationLocationDescription": "Select your destination location", "showCaseFindRiderDescription": "Find available riders", "buttonTitle": "Find Rider", "passengerHomeBottomSheetDestination": "Your Destinations", "passengerHomeDirectionErrorText": "Direction Route is null", "loginScreenAppHeaderTitle": "LogIn your Account", "loginScreenAppHeaderSubTitle": "Please enter your details to continue", "loginScreenPhoneNumberFormLabel": "Phone Number", "loginScreenPasswordFormLabel": "Password", "loginScreenForgetPasswordBtnTextLabel": "Forget Password?", "loginScreenSucessToastLabel": "<PERSON><PERSON>l", "loginScreenErrorToastLoginNotActiveLabel": "Please verify your number.<PERSON>gin not active yet.", "loginScreenButtonTitle": "<PERSON><PERSON>", "loginScreenDontHaveAnAccount": "Don't have an account?", "loginScreenSignUpButtonTitle": "Sign Up", "footerWidgetTextSpanByContinue": "By continuing, you agree to our ", "footerWidgetTextSpanTermsOfServices": "Terms of Service", "footerWidgetTextSpanAnd": " and ", "footerWidgetTextSpanPrivacyPolicy": "Privacy Policy", "signUpScreenAppHeaderTitle": "Create your Account", "signUpScreenAppHeaderSubTitle": "Please enter your details to sign up", "signUpScreenFullNameLabel": "Full Name", "signUpScreenPhoneNumberLabel": "Phone Number", "signUpScreenEmailAddressLabel": "Email Address", "signUpScreenCurrentAddressLabel": "Current Address", "signUpScreenGenderLabel": "Gender", "signUpScreenSignUpButtonTitle": "Sign Up", "signUpScreenAlreadyHaveAccountText": "Already have an account?", "signUpScreenSignInText": "Sign In", "verifyOtpScreenAppHeaderTitle": "Verify OTP", "verifyOtpScreenAppHeaderSubTitle": "Please enter the 4-digit OTP sent to your number.", "verifyOtpScreenResendButtonText": "Resend OTP", "verifyOtpScreenResendCountdownText": "Resend available in {_secondsRemaining} seconds", "verifyOtpScreenResendSuccessToast": "OTP resent successfully", "verifyOtpScreenResendFailedToast": "Failed to resend OTP", "verifyOtpScreenOtpVerifySuccessToast": "OTP verification successful", "verifyOtpScreenOtpVerifyFailedToast": "Incorrect OTP. Please try again.", "verifyOtpScreenPleaseLoginToast": "Please, login with your credentials."}