// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Nepali (`ne`).
class AppLocalizationsNe extends AppLocalizations {
  AppLocalizationsNe([String locale = 'ne']) : super(locale);

  @override
  String get appname => 'सफारी यात्री';

  @override
  String get welcomeback => 'फेरि स्वागत छ';

  @override
  String get signintocontinue => 'जारी राख्न साइन इन गर्नुहोस्';

  @override
  String get email => 'इमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get signin => 'साइन इन';

  @override
  String get signup => 'साइन अप';

  @override
  String get donthaveaccount => 'खाता छैन? साइन अप गर्नुहोस्';

  @override
  String get selectedLanguage => 'भाषा छान्नुहोस्';

  @override
  String get chooseLater =>
      'तपाईं सेटिङहरूबाट पछि भाषा परिवर्तन गर्न सक्नुहुन्छ।';

  @override
  String get next => 'अर्को';

  @override
  String get skip => 'छोड्नुहोस्';

  @override
  String get done => 'सकियो';

  @override
  String get onBoardingfirstScreenTitle => 'सबै सेवाहरूको लागि एउटा एप।';

  @override
  String get onBoardingSecondScreenTitle => 'समयमै त्यहाँ पुग्नुहोस्';

  @override
  String get onBoardingThirdScreenTitle => 'तिर्नुहोस, जस्तो चाहनुहुन्छ।';

  @override
  String get onBoardingfirstScreenDesc => 'आफ्नो औंलाको छेउमा सवारी गर्नुहोस्।';

  @override
  String get onBoardingSecondScreenDesc =>
      'ट्राफिकलाई कम गर्नुहोस् र हरेक पटक छिटो आफ्नो गन्तव्यमा पुग्नुहोस्।';

  @override
  String get onBoardingThirdScreenDesc =>
      'नगद? कार्ड? वालेट? हामी यो सबै स्वीकार गर्छौं। सुरु गरौं।';

  @override
  String get welcomeText => 'स्वागत छ';

  @override
  String get welcomeDescription => 'राइड सेयरिङ अनुभव अझ राम्रो बनाउनुहोस्';

  @override
  String get locationPermissionTitle => 'स्थान सुरक्षा';

  @override
  String get locationPermissionDescription =>
      'यो अप्लिकेशन उपयोग गर्नको लागि स्थान सुरक्षा गर्नुहोस्।';

  @override
  String get enableLocation => 'स्थान सुरक्षा गर्नुहोस्';

  @override
  String get locationEnableAccess => 'स्थान पहुँच सक्षम गर्नुहोस्';

  @override
  String get locationRequestingPermission => 'अनुमति अनुरोध गर्दै...';

  @override
  String get locationAccessGranted => 'स्थान पहुँच प्रदान गरियो';

  @override
  String get locationNeeded => 'स्थान आवश्यक छ';

  @override
  String get locationDescription =>
      'यो एपलाई राइडरहरूलाई ड्राइभरहरूसँग जोड्न र यात्रीहरूलाई यात्राहरू ट्र्याक गर्न मद्दत गर्न तपाईंको स्थान चाहिन्छ।';

  @override
  String get locationWaitingConfirmation =>
      'अनुमति संवादमा तपाईंको पुष्टिको लागि प्रतीक्षा गर्दै।';

  @override
  String get locationRedirecting =>
      'राम्रो! हामीले तपाईंको स्थान प्राप्त गर्यौं। अब तपाईंलाई पुनर्निर्देशित गर्दै...';

  @override
  String get locationDenied =>
      'तपाईंले स्थान पहुँच अस्वीकार गर्नुभयो। फेरि प्रयास गर्न तल ट्याप गर्नुहोस्।';

  @override
  String get locationPermanentlyDenied =>
      'स्थान स्थायी रूपमा अस्वीकार गरियो। कृपया पहुँच अनुमति दिन सेटिङहरू खोल्नुहोस्।';

  @override
  String get locationRequired => 'जारी राख्न स्थान अनुमति आवश्यक छ।';

  @override
  String get locationEnableButton => 'स्थान सक्षम गर्नुहोस्';

  @override
  String get locationOpenSettings => 'एप सेटिङहरू खोल्नुहोस्';

  @override
  String get locationTryAgain => 'पुन: प्रयास गर्नुहोस्';

  @override
  String get locationGoToSettings => 'सेटिङहरू खोल्नुहोस्';

  @override
  String get locationAllowAccess => 'स्थान पहुँच अनुमति दिनुहोस्';

  @override
  String get locationPermissionHeader => 'स्थान अनुमति आवश्यक छ';

  @override
  String get locationServiceDisabled => 'स्थान सेवाहरू अक्षम';

  @override
  String get locationServiceDisabledMessage =>
      'तपाईंको उपकरणमा स्थान सेवाहरू बन्द छन्। कृपया जारी राख्न तिनीहरूलाई सक्षम गर्नुहोस्।';

  @override
  String get openLocationSettings => 'स्थान सेटिङहरू खोल्नुहोस्';

  @override
  String get showCaseDrawerDescription =>
      'आफ्नो प्रोफाइल र सेटिङहरू हेर्नुहोस्';

  @override
  String get showCasePassengerCoutDescription =>
      'यात्रुको संख्या चयन गर्नुहोस्';

  @override
  String get showCasePickUpLocationDescription =>
      'आफ्नो पिकअप स्थान चयन गर्नुहोस्';

  @override
  String get showCaseDestinationLocationDescription =>
      'गन्तव्य स्थान चयन गर्नुहोस्';

  @override
  String get showCaseFindRiderDescription => 'उपलब्ध राइडरहरू खोज्नुहोस्';

  @override
  String get buttonTitle => 'राइडर खोज्नुहोस्';

  @override
  String get passengerHomeBottomSheetDestination => 'तपाईंको गन्तव्यहरू';

  @override
  String get passengerHomeDirectionErrorText => 'दिशा मार्ग फेला परेन';

  @override
  String get loginScreenAppHeaderTitle => 'आफ्नो खाता लगइन गर्नुहोस्';

  @override
  String get loginScreenAppHeaderSubTitle =>
      'कृपया जारी राख्न आफ्नो विवरणहरू प्रविष्ट गर्नुहोस्';

  @override
  String get loginScreenPhoneNumberFormLabel => 'फोन नम्बर';

  @override
  String get loginScreenPasswordFormLabel => 'पासवर्ड';

  @override
  String get loginScreenForgetPasswordBtnTextLabel => 'पासवर्ड बिर्सनुभयो?';

  @override
  String get loginScreenSucessToastLabel => 'लगइन सफल भयो';

  @override
  String get loginScreenErrorToastLoginNotActiveLabel =>
      'कृपया आफ्नो नम्बर प्रमाणिकरण गर्नुहोस्। लगइन अझै सक्रिय छैन।';

  @override
  String get loginScreenButtonTitle => 'लगइन';

  @override
  String get loginScreenDontHaveAnAccount => 'खाता छैन?';

  @override
  String get loginScreenSignUpButtonTitle => 'साइन अप गर्नुहोस्';

  @override
  String get footerWidgetTextSpanByContinue =>
      'जारी राखेर, तपाईं हाम्रो सँग सहमत हुनुहुन्छ ';

  @override
  String get footerWidgetTextSpanTermsOfServices => 'सेवाका सर्तहरू';

  @override
  String get footerWidgetTextSpanAnd => ' र ';

  @override
  String get footerWidgetTextSpanPrivacyPolicy => 'गोपनीयता नीति';

  @override
  String get signUpScreenAppHeaderTitle => 'आफ्नो खाता बनाउनुहोस्';

  @override
  String get signUpScreenAppHeaderSubTitle =>
      'साइन अप गर्न कृपया आफ्नो विवरणहरू प्रविष्ट गर्नुहोस्';

  @override
  String get signUpScreenFullNameLabel => 'पूरा नाम';

  @override
  String get signUpScreenPhoneNumberLabel => 'फोन नम्बर';

  @override
  String get signUpScreenEmailAddressLabel => 'इमेल ठेगाना';

  @override
  String get signUpScreenCurrentAddressLabel => 'हालको ठेगाना';

  @override
  String get signUpScreenGenderLabel => 'लिङ्ग';

  @override
  String get signUpScreenSignUpButtonTitle => 'साइन अप गर्नुहोस्';

  @override
  String get signUpScreenAlreadyHaveAccountText => 'पहिले नै खाता छ?';

  @override
  String get signUpScreenSignInText => 'साइन इन गर्नुहोस्';

  @override
  String get verifyOtpScreenAppHeaderTitle => 'ओटीपी प्रमाणिकरण गर्नुहोस्';

  @override
  String get verifyOtpScreenAppHeaderSubTitle =>
      'कृपया तपाईंको नम्बरमा पठाइएको ४-अंकीय ओटीपी प्रविष्ट गर्नुहोस्।';

  @override
  String get verifyOtpScreenResendButtonText => 'ओटीपी पुन: पठाउनुहोस्';

  @override
  String verifyOtpScreenResendCountdownText(Object _secondsRemaining) {
    return 'पुन: पठाउन \$_secondsRemaining सेकेन्ड बाँकी';
  }

  @override
  String get verifyOtpScreenResendSuccessToast =>
      'ओटीपी सफलतापूर्वक पुन: पठाइयो';

  @override
  String get verifyOtpScreenResendFailedToast => 'ओटीपी पुन: पठाउन असफल भयो';

  @override
  String get verifyOtpScreenOtpVerifySuccessToast => 'ओटीपी प्रमाणिकरण सफल भयो';

  @override
  String get verifyOtpScreenOtpVerifyFailedToast =>
      'ओटीपी गलत छ, कृपया फेरि प्रयास गर्नुहोस्';

  @override
  String get verifyOtpScreenPleaseLoginToast =>
      'कृपया आफ्नो लगइन विवरणहरू प्रयोग गरी लगइन गर्नुहोस्।';
}
