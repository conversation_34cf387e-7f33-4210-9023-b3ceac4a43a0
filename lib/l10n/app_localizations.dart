import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ne.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ne'),
  ];

  /// No description provided for @appname.
  ///
  /// In en, this message translates to:
  /// **'Safari Yatri'**
  String get appname;

  /// No description provided for @welcomeback.
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeback;

  /// No description provided for @signintocontinue.
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get signintocontinue;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @signin.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signin;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// No description provided for @donthaveaccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? Sign Up'**
  String get donthaveaccount;

  /// No description provided for @selectedLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectedLanguage;

  /// No description provided for @chooseLater.
  ///
  /// In en, this message translates to:
  /// **'You can change language later from settings'**
  String get chooseLater;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @onBoardingfirstScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'One app for all services.'**
  String get onBoardingfirstScreenTitle;

  /// No description provided for @onBoardingSecondScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Get there, on time'**
  String get onBoardingSecondScreenTitle;

  /// No description provided for @onBoardingThirdScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Pay, as you want.'**
  String get onBoardingThirdScreenTitle;

  /// No description provided for @onBoardingfirstScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Get a ride with your finertips.'**
  String get onBoardingfirstScreenDesc;

  /// No description provided for @onBoardingSecondScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Best the traffic and reach your destination fast, every time.'**
  String get onBoardingSecondScreenDesc;

  /// No description provided for @onBoardingThirdScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Cash? Card? Wallet? We accept it all. Let\'s get started.'**
  String get onBoardingThirdScreenDesc;

  /// No description provided for @welcomeText.
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcomeText;

  /// No description provided for @welcomeDescription.
  ///
  /// In en, this message translates to:
  /// **'Have a better sharing exprience'**
  String get welcomeDescription;

  /// No description provided for @locationPermissionTitle.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Permission'**
  String get locationPermissionTitle;

  /// No description provided for @locationPermissionDescription.
  ///
  /// In en, this message translates to:
  /// **'To get great service you need to provide location permission. You Can always change permission from settings.'**
  String get locationPermissionDescription;

  /// No description provided for @enableLocation.
  ///
  /// In en, this message translates to:
  /// **'Enable Location'**
  String get enableLocation;

  /// No description provided for @locationEnableAccess.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Access'**
  String get locationEnableAccess;

  /// No description provided for @locationRequestingPermission.
  ///
  /// In en, this message translates to:
  /// **'Requesting Permission...'**
  String get locationRequestingPermission;

  /// No description provided for @locationAccessGranted.
  ///
  /// In en, this message translates to:
  /// **'Location Access Granted'**
  String get locationAccessGranted;

  /// No description provided for @locationNeeded.
  ///
  /// In en, this message translates to:
  /// **'Location Needed'**
  String get locationNeeded;

  /// No description provided for @locationDescription.
  ///
  /// In en, this message translates to:
  /// **'This app needs your location to connect riders with drivers and help passengers track trips in real time.'**
  String get locationDescription;

  /// No description provided for @locationWaitingConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Waiting for your confirmation in the permission dialog.'**
  String get locationWaitingConfirmation;

  /// No description provided for @locationRedirecting.
  ///
  /// In en, this message translates to:
  /// **'Great! We\'ve got your location. Redirecting you now…'**
  String get locationRedirecting;

  /// No description provided for @locationDenied.
  ///
  /// In en, this message translates to:
  /// **'You denied location access. Tap below to try again.'**
  String get locationDenied;

  /// No description provided for @locationPermanentlyDenied.
  ///
  /// In en, this message translates to:
  /// **'Location permanently denied. Please open settings to allow access.'**
  String get locationPermanentlyDenied;

  /// No description provided for @locationRequired.
  ///
  /// In en, this message translates to:
  /// **'Location permission is required to continue.'**
  String get locationRequired;

  /// No description provided for @locationEnableButton.
  ///
  /// In en, this message translates to:
  /// **'Enable Location'**
  String get locationEnableButton;

  /// No description provided for @locationOpenSettings.
  ///
  /// In en, this message translates to:
  /// **'Open App Settings'**
  String get locationOpenSettings;

  /// No description provided for @locationTryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get locationTryAgain;

  /// No description provided for @locationGoToSettings.
  ///
  /// In en, this message translates to:
  /// **'Go to Settings'**
  String get locationGoToSettings;

  /// No description provided for @locationAllowAccess.
  ///
  /// In en, this message translates to:
  /// **'Allow Location Access'**
  String get locationAllowAccess;

  /// No description provided for @locationPermissionHeader.
  ///
  /// In en, this message translates to:
  /// **'Location Permission Required'**
  String get locationPermissionHeader;

  /// No description provided for @locationServiceDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location Services Disabled'**
  String get locationServiceDisabled;

  /// No description provided for @locationServiceDisabledMessage.
  ///
  /// In en, this message translates to:
  /// **'Location services are turned off on your device. Please enable them to continue.'**
  String get locationServiceDisabledMessage;

  /// No description provided for @openLocationSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Location Settings'**
  String get openLocationSettings;

  /// No description provided for @showCaseDrawerDescription.
  ///
  /// In en, this message translates to:
  /// **'View your profile and settings'**
  String get showCaseDrawerDescription;

  /// No description provided for @showCasePassengerCoutDescription.
  ///
  /// In en, this message translates to:
  /// **'Select the number of passengers'**
  String get showCasePassengerCoutDescription;

  /// No description provided for @showCasePickUpLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Select your pickup location'**
  String get showCasePickUpLocationDescription;

  /// No description provided for @showCaseDestinationLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Select your destination location'**
  String get showCaseDestinationLocationDescription;

  /// No description provided for @showCaseFindRiderDescription.
  ///
  /// In en, this message translates to:
  /// **'Find available riders'**
  String get showCaseFindRiderDescription;

  /// No description provided for @buttonTitle.
  ///
  /// In en, this message translates to:
  /// **'Find Rider'**
  String get buttonTitle;

  /// No description provided for @passengerHomeBottomSheetDestination.
  ///
  /// In en, this message translates to:
  /// **'Your Destinations'**
  String get passengerHomeBottomSheetDestination;

  /// No description provided for @passengerHomeDirectionErrorText.
  ///
  /// In en, this message translates to:
  /// **'Direction Route is null'**
  String get passengerHomeDirectionErrorText;

  /// No description provided for @loginScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'LogIn your Account'**
  String get loginScreenAppHeaderTitle;

  /// No description provided for @loginScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details to continue'**
  String get loginScreenAppHeaderSubTitle;

  /// No description provided for @loginScreenPhoneNumberFormLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get loginScreenPhoneNumberFormLabel;

  /// No description provided for @loginScreenPasswordFormLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get loginScreenPasswordFormLabel;

  /// No description provided for @loginScreenForgetPasswordBtnTextLabel.
  ///
  /// In en, this message translates to:
  /// **'Forget Password?'**
  String get loginScreenForgetPasswordBtnTextLabel;

  /// No description provided for @loginScreenSucessToastLabel.
  ///
  /// In en, this message translates to:
  /// **'Login Successfull'**
  String get loginScreenSucessToastLabel;

  /// No description provided for @loginScreenErrorToastLoginNotActiveLabel.
  ///
  /// In en, this message translates to:
  /// **'Please verify your number.Login not active yet.'**
  String get loginScreenErrorToastLoginNotActiveLabel;

  /// No description provided for @loginScreenButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginScreenButtonTitle;

  /// No description provided for @loginScreenDontHaveAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get loginScreenDontHaveAnAccount;

  /// No description provided for @loginScreenSignUpButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get loginScreenSignUpButtonTitle;

  /// No description provided for @footerWidgetTextSpanByContinue.
  ///
  /// In en, this message translates to:
  /// **'By continuing, you agree to our '**
  String get footerWidgetTextSpanByContinue;

  /// No description provided for @footerWidgetTextSpanTermsOfServices.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get footerWidgetTextSpanTermsOfServices;

  /// No description provided for @footerWidgetTextSpanAnd.
  ///
  /// In en, this message translates to:
  /// **' and '**
  String get footerWidgetTextSpanAnd;

  /// No description provided for @footerWidgetTextSpanPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get footerWidgetTextSpanPrivacyPolicy;

  /// No description provided for @signUpScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'Create your Account'**
  String get signUpScreenAppHeaderTitle;

  /// No description provided for @signUpScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details to sign up'**
  String get signUpScreenAppHeaderSubTitle;

  /// No description provided for @signUpScreenFullNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get signUpScreenFullNameLabel;

  /// No description provided for @signUpScreenPhoneNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get signUpScreenPhoneNumberLabel;

  /// No description provided for @signUpScreenEmailAddressLabel.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get signUpScreenEmailAddressLabel;

  /// No description provided for @signUpScreenCurrentAddressLabel.
  ///
  /// In en, this message translates to:
  /// **'Current Address'**
  String get signUpScreenCurrentAddressLabel;

  /// No description provided for @signUpScreenGenderLabel.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get signUpScreenGenderLabel;

  /// No description provided for @signUpScreenSignUpButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpScreenSignUpButtonTitle;

  /// No description provided for @signUpScreenAlreadyHaveAccountText.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get signUpScreenAlreadyHaveAccountText;

  /// No description provided for @signUpScreenSignInText.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signUpScreenSignInText;

  /// No description provided for @verifyOtpScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get verifyOtpScreenAppHeaderTitle;

  /// No description provided for @verifyOtpScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter the 4-digit OTP sent to your number.'**
  String get verifyOtpScreenAppHeaderSubTitle;

  /// No description provided for @verifyOtpScreenResendButtonText.
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get verifyOtpScreenResendButtonText;

  /// No description provided for @verifyOtpScreenResendCountdownText.
  ///
  /// In en, this message translates to:
  /// **'Resend available in {_secondsRemaining} seconds'**
  String verifyOtpScreenResendCountdownText(Object _secondsRemaining);

  /// No description provided for @verifyOtpScreenResendSuccessToast.
  ///
  /// In en, this message translates to:
  /// **'OTP resent successfully'**
  String get verifyOtpScreenResendSuccessToast;

  /// No description provided for @verifyOtpScreenResendFailedToast.
  ///
  /// In en, this message translates to:
  /// **'Failed to resend OTP'**
  String get verifyOtpScreenResendFailedToast;

  /// No description provided for @verifyOtpScreenOtpVerifySuccessToast.
  ///
  /// In en, this message translates to:
  /// **'OTP verification successful'**
  String get verifyOtpScreenOtpVerifySuccessToast;

  /// No description provided for @verifyOtpScreenOtpVerifyFailedToast.
  ///
  /// In en, this message translates to:
  /// **'Incorrect OTP. Please try again.'**
  String get verifyOtpScreenOtpVerifyFailedToast;

  /// No description provided for @verifyOtpScreenPleaseLoginToast.
  ///
  /// In en, this message translates to:
  /// **'Please, login with your credentials.'**
  String get verifyOtpScreenPleaseLoginToast;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'ne'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'ne':
      return AppLocalizationsNe();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
